import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const categorizeInvoice = tool({
  description: 'Assigns or re-assigns an invoice to a named category. Creates the category if it doesn\'t exist.',
  parameters: z.object({
    invoiceId: z.string().describe('The ID of the invoice to categorize'),
    categoryName: z.string().describe('The name of the category to assign'),
  }),
  execute: async ({ invoiceId, categoryName }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Find the invoice in the database - try both by ID and by invoice number
      let invoice = await db.invoice.findUnique({
        where: { id: invoiceId },
        include: { category: true },
      });

      // If not found by ID, try to find by invoice number
      if (!invoice) {
        invoice = await db.invoice.findFirst({
          where: {
            invoiceNumber: invoiceId,
            userId
          },
          include: { category: true },
        });
      }

      if (!invoice) {
        return {
          success: false,
          message: `Invoice with ID or number ${invoiceId} not found. Please check the ID and try again.`,
        };
      }

      // Check if the invoice belongs to the current user
      if (invoice.userId !== userId) {
        return {
          success: false,
          message: 'You do not have permission to update this invoice.',
        };
      }

      // Find or create the category
      let category = await db.category.findFirst({
        where: {
          name: {
            equals: categoryName,
            mode: 'insensitive',
          },
          userId: invoice.userId,
        },
      });

      // If the category doesn't exist, create it
      if (!category) {
        category = await db.category.create({
          data: {
            name: categoryName,
            userId: invoice.userId,
          },
        });
      }

      // Update the invoice with the category
      const updatedInvoice = await db.invoice.update({
        where: { id: invoiceId },
        data: {
          categoryId: category.id,
          updatedAt: new Date(),
        },
        include: { category: true },
      });

      return {
        id: updatedInvoice.id,
        number: updatedInvoice.invoiceNumber || '',
        category: category.name,
        message: `Invoice ${updatedInvoice.invoiceNumber || ''} has been categorized as "${category.name}".`,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to categorize invoice. Please try again.',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});

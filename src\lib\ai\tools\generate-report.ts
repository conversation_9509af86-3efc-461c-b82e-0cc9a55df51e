import { tool, DataStreamWriter } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';
import { generateUUID } from '@/lib/utils';
import { ReportType } from '@/lib/report-types';
import { generateReportData } from '@/lib/actions/report-data-generator';

interface GenerateReportProps {
  userId: string;
  dataStream: DataStreamWriter;
}

export const generateReport = ({ userId, dataStream }: GenerateReportProps) => tool({
  description: 'Builds a PDF report for various report types (Balance Sheet, Profit & Loss, Tax, Sales, Cash Flow, etc.), including charts and visualizations.',
  parameters: z.object({
    reportType: z.enum([
      ReportType.EXPENSES,
      ReportType.VENDOR_ANALYSIS,
      ReportType.CATEGORY_ANALYSIS,
      ReportType.CASH_FLOW,
      ReportType.SALES,
      ReportType.TAX,
      ReportType.CUSTOM,
      ReportType.INVOICE_SUMMARY,
      ReportType.PROFIT_LOSS,
      ReportType.CATEGORY_BREAKDOWN,
      ReportType.BALANCE_SHEET,
    ]).describe('The type of report to generate'),
    title: z.string().describe('The title of the report'),
    description: z.string().optional().describe('Optional description for the report'),
    startDate: z.string().optional().describe('Optional start date for the period to analyze (YYYY-MM-DD)'),
    endDate: z.string().optional().describe('Optional end date for the period to analyze (YYYY-MM-DD)'),
    currency: z.string().optional().describe('Optional currency to filter by (e.g., USD, EUR)'),
    format: z.enum(['PDF', 'CSV', 'EXCEL']).default('PDF').optional().describe('The format of the report'),
  }),
  execute: async ({
    reportType,
    title,
    description,
    startDate,
    endDate,
    currency,
    format = 'PDF'
  }) => {
    // --- OPTIMIZATION START ---
    const timeoutMs = 10000; // 10 seconds
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), timeoutMs);
    try {
      // Get the authenticated user ID
      const authenticatedUserId = userId || await getAuthenticatedUserId();

      // Generate a unique ID for the report
      const reportId = generateUUID();

      // Signal the start of report generation to the UI
      dataStream.writeData({
        type: 'report-generation-start',
        content: {
          id: reportId,
          reportType,
          title,
          format,
        },
      });

      // Update progress - Step 1: Initializing
      dataStream.writeData({
        type: 'report-generation-progress',
        content: {
          id: reportId,
          step: 1,
          totalSteps: 5,
          message: 'Initializing report generation...',
          percentage: 20,
        },
      });

      // Define more detailed progress steps based on report type
      let steps: string[] = [];

      // Customize steps based on report type
      switch (reportType) {
        case ReportType.BALANCE_SHEET:
          steps = [
            'Gathering asset and liability data',
            'Calculating equity and financial ratios',
            'Creating balance sheet visualizations',
            'Formatting balance sheet report',
            'Finalizing document'
          ];
          break;
        case ReportType.PROFIT_LOSS:
          steps = [
            'Gathering income and expense data',
            'Calculating profit margins and trends',
            'Creating profit & loss visualizations',
            'Formatting profit & loss report',
            'Finalizing document'
          ];
          break;
        case ReportType.TAX:
          steps = [
            'Gathering taxable transaction data',
            'Calculating tax amounts and rates',
            'Creating tax report visualizations',
            'Formatting tax report',
            'Finalizing document'
          ];
          break;
        case ReportType.SALES:
          steps = [
            'Gathering sales transaction data',
            'Analyzing sales trends and metrics',
            'Creating sales visualizations',
            'Formatting sales report',
            'Finalizing document'
          ];
          break;
        case ReportType.CASH_FLOW:
          steps = [
            'Gathering cash transaction data',
            'Analyzing cash flow patterns',
            'Creating cash flow visualizations',
            'Formatting cash flow report',
            'Finalizing document'
          ];
          break;
        default:
          steps = [
            'Gathering financial data',
            'Analyzing information',
            'Creating visualizations',
            'Formatting report',
            'Finalizing document'
          ];
      }

      for (let i = 0; i < steps.length; i++) {
        // Remove artificial delay for speed
        // await new Promise(resolve => setTimeout(resolve, 800));
        // Send progress update
        dataStream.writeData({
          type: 'report-generation-progress',
          content: {
            id: reportId,
            step: i + 1,
            totalSteps: steps.length,
            message: steps[i],
            percentage: Math.round(((i + 1) / steps.length) * 100),
          },
        });
      }

      // Save report metadata to the database first
      await db.report.create({
        data: {
          id: reportId,
          userId: authenticatedUserId,
          title,
          description: description || '',
          reportType,
          format,
          fileUrl: `/api/reports/${reportId}`,
          startDate: startDate ? new Date(startDate) : null,
          endDate: endDate ? new Date(endDate) : null,
          createdAt: new Date(),
        },
      });

      // Generate report data using our data generator (limit invoices for speed)
      await generateReportData(
        authenticatedUserId,
        reportId,
        reportType,
        startDate ? new Date(startDate) : null,
        endDate ? new Date(endDate) : null,
        currency,
        { limit: 20, signal: controller.signal } // pass limit and abort signal for optimization
      );

      // Get the report we just created with its data
      const reportWithData = await db.report.findUnique({
        where: { id: reportId },
        include: { data: true }
      });

      // Verify we have data
      if (!reportWithData?.data || reportWithData.data.length === 0) {
        // Try to regenerate data if none was created
        if (!reportWithData?.data || reportWithData.data.length === 0) {
          await generateReportData(
            authenticatedUserId,
            reportId,
            reportType,
            startDate ? new Date(startDate) : null,
            endDate ? new Date(endDate) : null,
            currency,
            { limit: 20, signal: controller.signal }
          );
        }
      }

      // --- OPTIMIZATION END ---
      // Continue with the rest of the logic as before
      // Generate file URL
      const fileUrl = `/api/reports/${reportId}`;

      // Signal completion
      dataStream.writeData({
        type: 'report-generation-complete',
        content: {
          id: reportId,
          title,
          reportType,
          format,
          fileUrl,
          createdAt: new Date().toISOString(),
        },
      });

      return {
        id: reportId,
        title,
        reportType,
        format,
        fileUrl,
        createdAt: new Date().toISOString(),
        message: `${format} report "${title}" generated successfully.`,
      };
    } catch (error: unknown) {
      if (error instanceof Error && error.name === 'AbortError') {
        dataStream.writeData({
          type: 'report-generation-error',
          content: { message: 'Report generation timed out. Please try again with a smaller date range or fewer filters.' },
        });
        throw new Error('Report generation timed out');
      } else {
        dataStream.writeData({
          type: 'report-generation-error',
          content: { message: 'An error occurred during report generation. Please try again.' },
        });
        throw error;
      }
    } finally {
      clearTimeout(timeout);
    }
  },
});

// All report data generation is now handled by the report-data-generator.ts file

import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';
import { ReportType } from '@/lib/report-types';

export const listReports = tool({
  description: 'Lists previously generated reports (with metadata such as report type, creation date, and download URL).',
  parameters: z.object({
    reportType: z.enum([
      ReportType.EXPENSES,
      ReportType.VENDOR_ANALYSIS,
      ReportType.CATEGORY_ANALYSIS,
      ReportType.CASH_FLOW,
      ReportType.SALES,
      ReportType.TAX,
      ReportType.CUSTOM,
      // Include legacy types for backward compatibility
      ReportType.INVOICE_SUMMARY,
      ReportType.PROFIT_LOSS,
      ReportType.CATEGORY_BREAKDOWN,
      ReportType.BALANCE_SHEET,
    ]).optional().describe('Optional filter for report type'),
    limit: z.number().min(1).max(100).default(20).optional().describe('Maximum number of reports to return'),
    page: z.number().min(1).default(1).optional().describe('Page number for pagination'),
    sortBy: z.enum(['createdAt', 'title', 'reportType']).default('createdAt').optional().describe('Field to sort by'),
    sortOrder: z.enum(['asc', 'desc']).default('desc').optional().describe('Sort order (ascending or descending)'),
  }),
  execute: async ({ reportType, limit = 20, page = 1, sortBy = 'createdAt', sortOrder = 'desc' }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Build the where clause for the query
      const where: Record<string, unknown> = {
        userId,
      };

      // Add report type filter if provided
      if (reportType) {
        where.reportType = reportType;
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Query the database for reports
      const [reports, total] = await Promise.all([
        db.report.findMany({
          where,
          orderBy: {
            [sortBy]: sortOrder,
          },
          skip,
          take: limit,
        }),
        db.report.count({ where }),
      ]);

      // Format the response
      const formattedReports = reports.map(report => ({
        id: report.id,
        title: report.title,
        description: report.description,
        reportType: report.reportType,
        format: report.format,
        fileUrl: report.fileUrl,
        startDate: report.startDate ? report.startDate.toISOString() : null,
        endDate: report.endDate ? report.endDate.toISOString() : null,
        createdAt: report.createdAt.toISOString(),
      }));

      return {
        reports: formattedReports,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to list reports. Please try again.',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

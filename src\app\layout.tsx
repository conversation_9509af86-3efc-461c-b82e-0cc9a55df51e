import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import './globals.css';
import { ThemeProvider } from '@/components/ai-agent/theme-provider';
import { ClerkProvider } from '@clerk/nextjs';
import { ToastProvider } from '@/components/ui/toast';
import { Toaster } from '@/components/ui/sonner';
// Removed LemonSqueezyProvider - using Paddle only
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/next';
import Script from 'next/script';
import Colors from '@/components/theme/Colors';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Billix AI | Intelligent Invoice Management',
  description:
    'AI-powered invoice processing and financial analysis tool that automates categorization, vendor matching, and financial insights for businesses of all sizes.',
  keywords: [
    'invoice management',
    'AI invoicing',
    'financial analysis',
    'invoice OCR',
    'expense tracking',
    'vendor management',
    'invoice automation',
  ],
  authors: [{ name: 'Billix AI' }],
  creator: 'Billix AI',
  publisher: 'Billix AI',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://www.billix.io',
    title: 'Billix AI | Intelligent Invoice Management',
    description:
      'AI-powered invoice processing and financial analysis tool that automates categorization, vendor matching, and financial insights for businesses of all sizes.',
    siteName: 'Billix AI',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Billix AI | Intelligent Invoice Management',
    description:
      'AI-powered invoice processing and financial analysis tool that automates categorization, vendor matching, and financial insights for businesses of all sizes.',
    creator: '@billixai',
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <head>
          <link rel="icon" href="/favicon.ico" sizes="any" />
          <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
          <meta name="theme-color" content={Colors.backgroundDark} />
          {/* Google tag (gtag.js) */}
          <Script
            async
            src="https://www.googletagmanager.com/gtag/js?id=AW-***********"
          />
          <Script id="google-analytics">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'AW-***********');
            `}
          </Script>
          {/* Twitter conversion tracking base code */}
          <Script id="twitter-pixel">
            {`
              !function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);
              },s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',
              a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');
              twq('config','poxym');
            `}
          </Script>
          {/* Twitter conversion tracking event code */}
          <Script id="twitter-event">
            {`
              // Insert Twitter Event ID
              twq('event', 'tw-poxym-poxyn', {
                value: null, // use this to pass the value of the conversion (e.g. 5.00)
                currency: null, // use this to pass the currency of the conversion with an ISO 4217 code (e.g. 'USD')
                contents: [ // use this to pass an array of products or content
                    // add all items to the array
                    // use this for the first item
                    {
                      content_type: null,
                      content_id: null,
                      content_name: null,
                      content_price: null,
                      num_items: null,
                      content_group_id: null
                    },
                    // use this for the second item
                    {
                      content_type: null,
                      content_id: null,
                      content_name: null,
                      content_price: null,
                      num_items: null,
                      content_group_id: null
                    }],
                conversion_id: null, // use this to pass a unique ID for the conversion event for deduplication (e.g. order id '1a2b3c')
                email_address: null, // use this to pass a user's email address
                phone_number: null // phone number in E164 standard
              });
            `}
          </Script>
        </head>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
          suppressHydrationWarning
        >
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <ToastProvider>
              {children}
              <Toaster />
            </ToastProvider>
          </ThemeProvider>
          <Analytics />
          <SpeedInsights />
        </body>
      </html>
    </ClerkProvider>
  );
}

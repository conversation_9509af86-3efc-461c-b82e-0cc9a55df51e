import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';
import { InvoiceStatus } from '@prisma/client';

// Define a type for the selected invoice fields
type SelectedInvoice = {
  id: string;
  amount: number | null;
  currency: string | null;
  status: InvoiceStatus;
  issueDate: Date | null;
  dueDate: Date | null;
};

export const balanceSheetSnapshot = tool({
  description: 'Creates a snapshot of assets (e.g., outstanding receivables) and liabilities as of a given date, yielding a simple balance sheet.',
  parameters: z.object({
    asOfDate: z.string().describe('The date for which to generate the balance sheet snapshot (YYYY-MM-DD)'),
    currency: z.string().optional().describe('Optional currency to filter by (e.g., USD, EUR)'),
    includeEstimatedLiabilities: z.boolean().default(false).optional().describe('Whether to include estimated liabilities based on unpaid invoices'),
    liabilityEstimatePercentage: z.number().min(0).max(100).default(30).optional().describe('Estimated liability percentage (0-100)'),
  }),
  execute: async ({ 
    asOfDate, 
    currency, 
    includeEstimatedLiabilities = false, 
    liabilityEstimatePercentage = 30 
  }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();
      
      // Convert asOfDate to Date object
      const snapshotDate = new Date(asOfDate);
      
      // Build the where clause for the query
      const baseWhere: Record<string, unknown> = {
        userId,
      };
      
      // Add currency filter if provided
      if (currency) {
        baseWhere.currency = currency;
      }

      // Get all invoices for assets (receivables) - invoices issued before or on the snapshot date
      const assetsWhere = {
        ...baseWhere,
        issueDate: {
          lte: snapshotDate,
        },
      };
      
      const assetInvoices = await db.invoice.findMany({
        where: assetsWhere,
        select: {
          id: true,
          amount: true,
          currency: true,
          status: true,
          issueDate: true,
          dueDate: true,
        },
      });

      // Get all invoices for liabilities - if including estimated liabilities
      let liabilityInvoices: SelectedInvoice[] = [];
      if (includeEstimatedLiabilities) {
        const liabilitiesWhere = {
          ...baseWhere,
          issueDate: {
            lte: snapshotDate,
          },
          status: {
            not: InvoiceStatus.PAID,
          },
        };
        
        liabilityInvoices = await db.invoice.findMany({
          where: liabilitiesWhere,
          select: {
            id: true,
            amount: true,
            currency: true,
            status: true,
            issueDate: true,
            dueDate: true,
          },
        });
      }

      // Initialize balance sheet data
      const balanceSheet = {
        assets: {
          accountsReceivable: {
            total: 0,
            current: 0, // Not yet due
            overdue: 0, // Past due date
            aging: {
              '0-30': 0,
              '31-60': 0,
              '61-90': 0,
              '90+': 0,
            },
            byCurrency: {} as Record<string, number>,
          },
          totalAssets: 0,
        },
        liabilities: {
          accountsPayable: {
            total: 0,
            byCurrency: {} as Record<string, number>,
          },
          totalLiabilities: 0,
        },
        equity: {
          netWorth: 0,
        },
      };

      // Process asset invoices (receivables)
      for (const invoice of assetInvoices) {
        if (invoice.status !== InvoiceStatus.PAID && invoice.status !== InvoiceStatus.CANCELLED) {
          const amount = invoice.amount || 0;
          const currencyKey = invoice.currency || 'UNKNOWN';
          
          // Update accounts receivable
          balanceSheet.assets.accountsReceivable.total += amount;
          
          // Update currency breakdown
          if (!balanceSheet.assets.accountsReceivable.byCurrency[currencyKey]) {
            balanceSheet.assets.accountsReceivable.byCurrency[currencyKey] = 0;
          }
          balanceSheet.assets.accountsReceivable.byCurrency[currencyKey] += amount;
          
          // Check if overdue
          const isDueDate = invoice.dueDate ? new Date(invoice.dueDate) : null;
          if (isDueDate && isDueDate < snapshotDate) {
            balanceSheet.assets.accountsReceivable.overdue += amount;
            
            // Calculate aging
            const daysOverdue = Math.floor((snapshotDate.getTime() - isDueDate.getTime()) / (1000 * 60 * 60 * 24));
            if (daysOverdue <= 30) {
              balanceSheet.assets.accountsReceivable.aging['0-30'] += amount;
            } else if (daysOverdue <= 60) {
              balanceSheet.assets.accountsReceivable.aging['31-60'] += amount;
            } else if (daysOverdue <= 90) {
              balanceSheet.assets.accountsReceivable.aging['61-90'] += amount;
            } else {
              balanceSheet.assets.accountsReceivable.aging['90+'] += amount;
            }
          } else {
            balanceSheet.assets.accountsReceivable.current += amount;
          }
        }
      }

      // Update total assets
      balanceSheet.assets.totalAssets = balanceSheet.assets.accountsReceivable.total;

      // Process liability invoices if including estimated liabilities
      if (includeEstimatedLiabilities) {
        for (const invoice of liabilityInvoices) {
          const amount = invoice.amount || 0;
          const estimatedLiability = amount * (liabilityEstimatePercentage / 100);
          const currencyKey = invoice.currency || 'UNKNOWN';
          
          // Update accounts payable
          balanceSheet.liabilities.accountsPayable.total += estimatedLiability;
          
          // Update currency breakdown
          if (!balanceSheet.liabilities.accountsPayable.byCurrency[currencyKey]) {
            balanceSheet.liabilities.accountsPayable.byCurrency[currencyKey] = 0;
          }
          balanceSheet.liabilities.accountsPayable.byCurrency[currencyKey] += estimatedLiability;
        }
      }

      // Update total liabilities
      balanceSheet.liabilities.totalLiabilities = balanceSheet.liabilities.accountsPayable.total;

      // Calculate net worth (equity)
      balanceSheet.equity.netWorth = balanceSheet.assets.totalAssets - balanceSheet.liabilities.totalLiabilities;

      // Prepare the result
      return {
        balanceSheet,
        snapshotDate: snapshotDate.toISOString(),
        liabilityEstimation: includeEstimatedLiabilities 
          ? `${liabilityEstimatePercentage}% of unpaid invoices` 
          : 'No liability estimation',
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to generate balance sheet snapshot. Please try again.',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

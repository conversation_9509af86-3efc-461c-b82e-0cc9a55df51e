import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const getVendorSpendAnalysis = tool({
  description: 'Calculates total spend per vendor (optionally filtered by vendor or timeframe), ranking top vendors and showing trends.',
  parameters: z.object({
    startDate: z.string().optional().describe('Optional start date for the period to analyze (YYYY-MM-DD)'),
    endDate: z.string().optional().describe('Optional end date for the period to analyze (YYYY-MM-DD)'),
    vendorName: z.string().optional().describe('Optional vendor name to filter by'),
    currency: z.string().optional().describe('Optional currency to filter by (e.g., USD, EUR)'),
    limit: z.number().min(1).max(100).default(10).optional().describe('Maximum number of vendors to return in the analysis'),
  }),
  execute: async ({ startDate, endDate, vendorName, currency, limit = 10 }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Build the where clause for the query
      const where: Record<string, unknown> = {
        userId,
        status: {
          not: 'CANCELLED', // Exclude cancelled invoices
        },
      };

      // Add date filters if provided
      if (startDate) {
        where.issueDate = {
          ...(where.issueDate || {}),
          gte: new Date(startDate),
        };
      }

      if (endDate) {
        where.issueDate = {
          ...(where.issueDate || {}),
          lte: new Date(endDate),
        };
      }

      // Add vendor filter if provided
      if (vendorName) {
        where.vendorName = {
          contains: vendorName,
          mode: 'insensitive',
        };
      }

      // Add currency filter if provided
      if (currency) {
        where.currency = currency;
      }

      // Get all invoices for the user with the applied filters
      const invoices = await db.invoice.findMany({
        where,
        select: {
          id: true,
          vendorName: true,
          amount: true,
          currency: true,
          issueDate: true,
        },
        orderBy: {
          issueDate: 'asc',
        },
      });

      // Group invoices by vendor
      const vendorSpend: Record<string, {
        totalSpend: number;
        invoiceCount: number;
        currencies: Record<string, number>;
        monthlyTrend: Record<string, number>;
      }> = {};

      // Calculate vendor spend
      for (const invoice of invoices) {
        const vendorKey = invoice.vendorName || 'Unknown Vendor';
        const amount = invoice.amount || 0;
        const currencyKey = invoice.currency || 'UNKNOWN';
        
        // Create vendor entry if it doesn't exist
        if (!vendorSpend[vendorKey]) {
          vendorSpend[vendorKey] = {
            totalSpend: 0,
            invoiceCount: 0,
            currencies: {},
            monthlyTrend: {},
          };
        }

        // Update vendor stats
        vendorSpend[vendorKey].totalSpend += amount;
        vendorSpend[vendorKey].invoiceCount++;

        // Update currency breakdown
        if (!vendorSpend[vendorKey].currencies[currencyKey]) {
          vendorSpend[vendorKey].currencies[currencyKey] = 0;
        }
        vendorSpend[vendorKey].currencies[currencyKey] += amount;

        // Update monthly trend
        if (invoice.issueDate) {
          const monthYear = `${invoice.issueDate.getFullYear()}-${String(invoice.issueDate.getMonth() + 1).padStart(2, '0')}`;
          if (!vendorSpend[vendorKey].monthlyTrend[monthYear]) {
            vendorSpend[vendorKey].monthlyTrend[monthYear] = 0;
          }
          vendorSpend[vendorKey].monthlyTrend[monthYear] += amount;
        }
      }

      // Convert to array and sort by total spend
      const vendorAnalysis = Object.entries(vendorSpend)
        .map(([vendorName, data]) => ({
          vendorName,
          ...data,
          averageInvoiceAmount: data.invoiceCount > 0 ? data.totalSpend / data.invoiceCount : 0,
        }))
        .sort((a, b) => b.totalSpend - a.totalSpend)
        .slice(0, limit);

      // Calculate total spend across all vendors
      const totalSpend = vendorAnalysis.reduce((sum, vendor) => sum + vendor.totalSpend, 0);

      // Calculate percentages of total spend
      const vendorsWithPercentage = vendorAnalysis.map(vendor => ({
        ...vendor,
        percentageOfTotalSpend: totalSpend > 0 ? (vendor.totalSpend / totalSpend) * 100 : 0,
      }));

      // Prepare the result
      return {
        vendors: vendorsWithPercentage,
        totalVendors: Object.keys(vendorSpend).length,
        totalSpend,
        timeframe: {
          startDate: startDate ? new Date(startDate).toISOString() : null,
          endDate: endDate ? new Date(endDate).toISOString() : null,
        },
        topVendor: vendorsWithPercentage.length > 0 ? vendorsWithPercentage[0] : null,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to analyze vendor spend. Please try again.',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

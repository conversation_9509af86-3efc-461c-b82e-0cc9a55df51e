import { Redis } from '@upstash/redis';

// Multi-layer caching system for optimal performance
interface CacheConfig {
  ttl: number; // Time to live in seconds
  layer: 'memory' | 'redis' | 'both';
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

/**
 * Advanced Multi-Layer Cache Engine
 * Provides memory + Redis caching for maximum performance
 */
export class CacheEngine {
  private static memoryCache = new Map<string, CacheEntry<any>>();
  private static redis: Redis | null = null;
  
  // Cache configurations for different data types
  private static configs: Record<string, CacheConfig> = {
    'user-context': { ttl: 300, layer: 'both' }, // 5 minutes
    'conversation-memory': { ttl: 1800, layer: 'both' }, // 30 minutes
    'user-profile': { ttl: 3600, layer: 'both' }, // 1 hour
    'invoice-stats': { ttl: 60, layer: 'memory' }, // 1 minute
    'model-selection': { ttl: 30, layer: 'memory' }, // 30 seconds
    'action-results': { ttl: 10, layer: 'memory' }, // 10 seconds
  };

  static {
    // Initialize Redis if available
    if (process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN) {
      this.redis = new Redis({
        url: process.env.UPSTASH_REDIS_REST_URL,
        token: process.env.UPSTASH_REDIS_REST_TOKEN,
      });
    }
  }

  /**
   * Get cached data with automatic fallback
   */
  static async get<T>(key: string, type: string): Promise<T | null> {
    const config = this.configs[type] || { ttl: 300, layer: 'memory' };
    
    try {
      // Try memory cache first (fastest)
      if (config.layer === 'memory' || config.layer === 'both') {
        const memoryResult = this.getFromMemory<T>(key, config.ttl);
        if (memoryResult !== null) {
          return memoryResult;
        }
      }

      // Try Redis cache (if available and configured)
      if ((config.layer === 'redis' || config.layer === 'both') && this.redis) {
        const redisResult = await this.getFromRedis<T>(key, config.ttl);
        if (redisResult !== null) {
          // Store in memory for faster access next time
          this.setInMemory(key, redisResult, config.ttl);
          return redisResult;
        }
      }

      return null;
    } catch (error) {
      console.error(`Cache get error for ${key}:`, error);
      return null;
    }
  }

  /**
   * Set cached data with automatic distribution
   */
  static async set<T>(key: string, data: T, type: string): Promise<void> {
    const config = this.configs[type] || { ttl: 300, layer: 'memory' };
    
    try {
      // Set in memory cache
      if (config.layer === 'memory' || config.layer === 'both') {
        this.setInMemory(key, data, config.ttl);
      }

      // Set in Redis cache (if available and configured)
      if ((config.layer === 'redis' || config.layer === 'both') && this.redis) {
        await this.setInRedis(key, data, config.ttl);
      }
    } catch (error) {
      console.error(`Cache set error for ${key}:`, error);
    }
  }

  /**
   * Delete cached data from all layers
   */
  static async delete(key: string): Promise<void> {
    try {
      // Delete from memory
      this.memoryCache.delete(key);

      // Delete from Redis
      if (this.redis) {
        await this.redis.del(key);
      }
    } catch (error) {
      console.error(`Cache delete error for ${key}:`, error);
    }
  }

  /**
   * Clear all caches
   */
  static async clear(pattern?: string): Promise<void> {
    try {
      if (pattern) {
        // Clear specific pattern
        const keysToDelete = Array.from(this.memoryCache.keys()).filter(key => 
          key.includes(pattern)
        );
        keysToDelete.forEach(key => this.memoryCache.delete(key));

        if (this.redis) {
          const redisKeys = await this.redis.keys(`*${pattern}*`);
          if (redisKeys.length > 0) {
            await this.redis.del(...redisKeys);
          }
        }
      } else {
        // Clear all
        this.memoryCache.clear();
        if (this.redis) {
          await this.redis.flushall();
        }
      }
    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  /**
   * Get cache statistics
   */
  static getStats(): {
    memorySize: number;
    memoryKeys: string[];
    redisAvailable: boolean;
  } {
    return {
      memorySize: this.memoryCache.size,
      memoryKeys: Array.from(this.memoryCache.keys()),
      redisAvailable: this.redis !== null,
    };
  }

  /**
   * Memory cache operations
   */
  private static getFromMemory<T>(key: string, ttl: number): T | null {
    const entry = this.memoryCache.get(key);
    if (!entry) return null;

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl * 1000) {
      this.memoryCache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  private static setInMemory<T>(key: string, data: T, ttl: number): void {
    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });

    // Cleanup old entries periodically
    if (this.memoryCache.size > 1000) {
      this.cleanupMemoryCache();
    }
  }

  /**
   * Redis cache operations
   */
  private static async getFromRedis<T>(key: string, ttl: number): Promise<T | null> {
    if (!this.redis) return null;

    try {
      const data = await this.redis.get(key);
      return data ? JSON.parse(data as string) : null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }

  private static async setInRedis<T>(key: string, data: T, ttl: number): Promise<void> {
    if (!this.redis) return;

    try {
      await this.redis.setex(key, ttl, JSON.stringify(data));
    } catch (error) {
      console.error('Redis set error:', error);
    }
  }

  /**
   * Cleanup expired memory cache entries
   */
  private static cleanupMemoryCache(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.memoryCache.entries()) {
      if (now - entry.timestamp > entry.ttl * 1000) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.memoryCache.delete(key));
  }

  /**
   * Intelligent cache key generation
   */
  static generateKey(type: string, ...identifiers: string[]): string {
    return `billix:${type}:${identifiers.join(':')}`;
  }

  /**
   * Cache-aware data fetcher with automatic caching
   */
  static async getOrSet<T>(
    key: string,
    type: string,
    fetcher: () => Promise<T>
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key, type);
    if (cached !== null) {
      return cached;
    }

    // Fetch fresh data
    const data = await fetcher();
    
    // Cache the result
    await this.set(key, data, type);
    
    return data;
  }
}

/**
 * Cache decorators for easy integration
 */
export function cached(type: string, keyGenerator?: (...args: any[]) => string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const key = keyGenerator 
        ? CacheEngine.generateKey(type, keyGenerator(...args))
        : CacheEngine.generateKey(type, JSON.stringify(args));

      return CacheEngine.getOrSet(key, type, () => method.apply(this, args));
    };

    return descriptor;
  };
}

/**
 * Smart cache warming for frequently accessed data
 */
export class CacheWarmer {
  private static warmupTasks: Array<{
    key: string;
    type: string;
    fetcher: () => Promise<any>;
    interval: number;
  }> = [];

  static addWarmupTask(
    key: string,
    type: string,
    fetcher: () => Promise<any>,
    intervalMinutes: number = 5
  ) {
    this.warmupTasks.push({
      key,
      type,
      fetcher,
      interval: intervalMinutes * 60 * 1000,
    });
  }

  static startWarming() {
    this.warmupTasks.forEach(task => {
      // Initial warmup
      this.warmupCache(task);
      
      // Schedule periodic warmup
      setInterval(() => this.warmupCache(task), task.interval);
    });
  }

  private static async warmupCache(task: {
    key: string;
    type: string;
    fetcher: () => Promise<any>;
  }) {
    try {
      const data = await task.fetcher();
      await CacheEngine.set(task.key, data, task.type);
    } catch (error) {
      console.error(`Cache warmup failed for ${task.key}:`, error);
    }
  }
}

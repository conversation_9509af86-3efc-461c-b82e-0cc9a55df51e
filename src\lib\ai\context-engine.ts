import db from '@/db/db';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import { CacheEngine } from './cache-engine';

interface UserContext {
  user: {
    name: string;
    email: string;
    role: string;
    preferences: any;
  };
  business: {
    totalInvoices: number;
    totalRevenue: number;
    pendingAmount: number;
    overdueCount: number;
    recentInvoices: any[];
    topVendors: any[];
    monthlyTrends: any[];
  };
  quickActions: string[];
  recentActivity: any[];
}

/**
 * Smart Context Engine - Replaces tools with intelligent context injection
 * This provides the AI with all necessary context in a single, optimized payload
 */
export class ContextEngine {

  static async getUserContext(userId: string): Promise<UserContext> {
    const cacheKey = CacheEngine.generateKey('user-context', userId);

    return CacheEngine.getOrSet(
      cacheKey,
      'user-context',
      () => this.buildUserContext(userId)
    );
  }

  private static async buildUserContext(userId: string): Promise<UserContext> {
    try {
      // Parallel data fetching for speed
      const [user, invoiceStats, recentInvoices, topVendors] = await Promise.all([
        db.user.findUnique({
          where: { id: userId },
          select: {
            firstName: true,
            lastName: true,
            email: true,
            role: true,
            aiSettings: true,
          }
        }),
        
        // Get invoice statistics
        db.invoice.aggregate({
          where: { userId },
          _count: { id: true },
          _sum: { amount: true },
        }),
        
        // Get recent invoices
        db.invoice.findMany({
          where: { userId },
          orderBy: { createdAt: 'desc' },
          take: 5,
          select: {
            id: true,
            invoiceNumber: true,
            vendorName: true,
            amount: true,
            status: true,
            createdAt: true,
          }
        }),
        
        // Get top vendors
        db.invoice.groupBy({
          by: ['vendorName'],
          where: { userId, vendorName: { not: null } },
          _sum: { amount: true },
          _count: { id: true },
          orderBy: { _sum: { amount: 'desc' } },
          take: 3,
        })
      ]);

      // Get overdue count
      const overdueCount = await db.invoice.count({
        where: { userId, status: 'OVERDUE' }
      });

      // Calculate pending amount
      const pendingAmount = await db.invoice.aggregate({
        where: { userId, status: 'PENDING' },
        _sum: { amount: true }
      });

      return {
        user: {
          name: `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'User',
          email: user?.email || '',
          role: user?.role || 'USER',
          preferences: user?.aiSettings || {},
        },
        business: {
          totalInvoices: invoiceStats._count.id || 0,
          totalRevenue: invoiceStats._sum.amount || 0,
          pendingAmount: pendingAmount._sum.amount || 0,
          overdueCount,
          recentInvoices: recentInvoices.map(inv => ({
            number: inv.invoiceNumber,
            vendor: inv.vendorName,
            amount: inv.amount,
            status: inv.status,
            date: inv.createdAt.toISOString().split('T')[0]
          })),
          topVendors: topVendors.map(v => ({
            name: v.vendorName,
            total: v._sum.amount,
            count: v._count.id
          })),
          monthlyTrends: [], // Will be populated with trend data
        },
        quickActions: [
          'Create new invoice',
          'View pending invoices', 
          'Generate monthly report',
          'Check cash flow',
          'Analyze spending'
        ],
        recentActivity: [] // Recent user actions
      };
    } catch (error) {
      console.error('Context Engine Error:', error);
      return this.getEmptyContext();
    }
  }

  private static getEmptyContext(): UserContext {
    return {
      user: { name: 'User', email: '', role: 'USER', preferences: {} },
      business: {
        totalInvoices: 0,
        totalRevenue: 0,
        pendingAmount: 0,
        overdueCount: 0,
        recentInvoices: [],
        topVendors: [],
        monthlyTrends: []
      },
      quickActions: [],
      recentActivity: []
    };
  }

  static async clearCache(userId?: string) {
    if (userId) {
      const cacheKey = CacheEngine.generateKey('user-context', userId);
      await CacheEngine.delete(cacheKey);
    } else {
      await CacheEngine.clear('user-context');
    }
  }
}

/**
 * Generate optimized system prompt with user context
 */
export function generateContextualPrompt(context: UserContext): string {
  return `You are Billix AI, ${context.user.name}'s intelligent financial assistant.

CURRENT CONTEXT:
• Business: ${context.business.totalInvoices} invoices, $${context.business.totalRevenue.toLocaleString()} total revenue
• Pending: $${context.business.pendingAmount.toLocaleString()} (${context.business.overdueCount} overdue)
• Recent: ${context.business.recentInvoices.map(i => `${i.vendor}: $${i.amount}`).join(', ')}
• Top vendors: ${context.business.topVendors.map(v => v.name).join(', ')}

CAPABILITIES:
• Invoice management (create, track, organize)
• Financial reporting and analytics  
• Cash flow analysis and forecasting
• Vendor and spending insights

Be conversational, helpful, and proactive. Reference specific data when relevant. Ask clarifying questions for complex requests.`;
}

import { DataStreamWriter, tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { generateUUID } from '@/lib/utils';
import { InvoiceStatus } from '@prisma/client';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

interface CreateInvoiceProps {
  dataStream: DataStreamWriter;
  userId: string;
}                                             

// Invoice templates for quick creation
const INVOICE_TEMPLATES = [
  {
    id: 'standard',
    name: 'Standard Invoice',
    description: 'A standard invoice template for general use',
    lineItems: [
      { description: 'Product or Service', quantity: 1, unitPrice: 100 }
    ]
  },
  {
    id: 'detailed',
    name: 'Detailed Invoice',
    description: 'A detailed invoice with multiple line items',
    lineItems: [
      { description: 'Product A', quantity: 2, unitPrice: 50 },
      { description: 'Service B', quantity: 1, unitPrice: 150 },
      { description: 'Maintenance', quantity: 3, unitPrice: 75 }
    ]
  },
  {
    id: 'consulting',
    name: 'Consulting Invoice',
    description: 'Template for consulting services',
    lineItems: [
      { description: 'Consulting Hours', quantity: 10, unitPrice: 125 },
      { description: 'Travel Expenses', quantity: 1, unitPrice: 250 }
    ]
  }
];

export const createInvoice = ({ dataStream, userId }: CreateInvoiceProps) =>
  tool({
    description: 'Creates a new invoice record with line items in the database and attaches it to the authenticated user. Can also edit existing invoices.',
    parameters: z.object({
      vendorName: z.string().describe('The name of the vendor or supplier'),
      invoiceNumber: z.string().describe('The invoice number or identifier'),
      currency: z.string().default('USD').describe('The currency code (e.g., USD, EUR)'),
      issueDate: z.string().describe('The date the invoice was issued (YYYY-MM-DD)'),
      dueDate: z.string().describe('The date the invoice is due (YYYY-MM-DD)'),
      category: z.string().optional().describe('Optional category for the invoice'),
      notes: z.string().optional().describe('Optional notes about the invoice'),
      lineItems: z.array(
        z.object({
          description: z.string().describe('Description of the item'),
          quantity: z.number().describe('Quantity of items'),
          unitPrice: z.number().describe('Price per unit'),
        })
      ).describe('Line items for the invoice'),
      templateId: z.string().optional().describe('ID of the template to use (standard, detailed, consulting)'),
      existingInvoiceId: z.string().optional().describe('ID of an existing invoice to edit instead of creating a new one'),
    }),
    execute: async ({
      vendorName,
      invoiceNumber,
      currency,
      issueDate,
      dueDate,
      category,
      notes,
      lineItems,
      templateId,
      existingInvoiceId
    }) => {
      try {
        // Get the authenticated user ID - use the provided userId if available, otherwise get from auth
        const authenticatedUserId = userId || await getAuthenticatedUserId();

        // If a template ID is provided but no line items, use the template
        if (templateId && (!lineItems || lineItems.length === 0)) {
          const template = INVOICE_TEMPLATES.find(t => t.id === templateId);
          if (template) {
            lineItems = template.lineItems;

            // Send template selection to the UI
            dataStream.writeData({
              type: 'invoice-template-selected',
              content: {
                templateId,
                templateName: template.name,
                lineItems
              },
            });
          }
        }

        // Check if we're editing an existing invoice
        let isEditing = false;
        let existingInvoice = null;
        let id = generateUUID();

        if (existingInvoiceId) {
          // Try to find the invoice by ID
          existingInvoice = await db.invoice.findUnique({
            where: { id: existingInvoiceId },
            include: { lineItems: true, category: true },
          });

          // If not found by ID, try by invoice number
          if (!existingInvoice) {
            existingInvoice = await db.invoice.findFirst({
              where: {
                invoiceNumber: existingInvoiceId,
                userId: authenticatedUserId
              },
              include: { lineItems: true, category: true },
            });
          }

          if (existingInvoice) {
            isEditing = true;
            id = existingInvoice.id;

            // If no line items provided, use existing ones
            if (!lineItems || lineItems.length === 0) {
              lineItems = existingInvoice.lineItems.map(item => ({
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
              }));
            }

            // Use existing values for any missing fields
            vendorName = vendorName || existingInvoice.vendorName || '';
            invoiceNumber = invoiceNumber || existingInvoice.invoiceNumber || '';
            currency = currency || existingInvoice.currency || 'USD';
            issueDate = issueDate || (existingInvoice.issueDate ? existingInvoice.issueDate.toISOString().split('T')[0] : '');
            dueDate = dueDate || (existingInvoice.dueDate ? existingInvoice.dueDate.toISOString().split('T')[0] : '');
            notes = notes !== undefined ? notes : existingInvoice.notes || '';

            if (!category && existingInvoice.category) {
              category = existingInvoice.category.name;
            }
          }
        }

        // Signal the start of invoice creation/editing to the UI
        dataStream.writeData({
          type: isEditing ? 'invoice-editing-start' : 'invoice-creation-start',
          content: {
            id,
            vendorName,
            invoiceNumber,
            isEditing,
            existingData: isEditing ? {
              currency: currency || 'USD',
              issueDate: issueDate || '',
              dueDate: dueDate || '',
              category: category || '',
              notes: notes || ''
            } : null
          },
        });

        // Calculate line item amounts and total
        const calculatedLineItems = lineItems.map(item => {
          const totalPrice = item.quantity * item.unitPrice;
          return {
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice,
          };
        });

        const totalAmount = calculatedLineItems.reduce((sum, item) => sum + item.totalPrice, 0);

        // Find or create category if provided
        let categoryId: string | undefined;

        if (category) {
          // Look for existing category with the same name
          const existingCategory = await db.category.findFirst({
            where: {
              name: {
                equals: category,
                mode: 'insensitive',
              },
              userId,
            },
          });

          if (existingCategory) {
            categoryId = existingCategory.id;
          } else {
            // Create a new category
            const newCategory = await db.category.create({
              data: {
                name: category,
                userId,
              },
            });

            categoryId = newCategory.id;
          }
        }

        // Create or update the invoice in the database using a transaction
        const invoice = await db.$transaction(async (prisma) => {
          let result;

          if (isEditing && existingInvoice) {
            // Update the existing invoice
            result = await prisma.invoice.update({
              where: { id: existingInvoice.id },
              data: {
                invoiceNumber,
                vendorName,
                amount: totalAmount,
                currency,
                categoryId,
                issueDate: new Date(issueDate),
                dueDate: new Date(dueDate),
                notes,
                updatedAt: new Date(),
              },
            });

            // Delete existing line items
            await prisma.invoiceLineItem.deleteMany({
              where: { invoiceId: existingInvoice.id },
            });

          } else {
            // Create a new invoice
            result = await prisma.invoice.create({
              data: {
                invoiceNumber,
                vendorName,
                amount: totalAmount,
                currency,
                status: 'PENDING' as InvoiceStatus,
                categoryId,
                issueDate: new Date(issueDate),
                dueDate: new Date(dueDate),
                notes,
                userId: authenticatedUserId,
                // Don't create line items yet
              },
            });
          }

          // Create line items
          await Promise.all(
            calculatedLineItems.map(item =>
              prisma.invoiceLineItem.create({
                data: {
                  invoiceId: result.id,
                  description: item.description,
                  quantity: item.quantity,
                  unitPrice: item.unitPrice,
                  totalPrice: item.totalPrice,
                },
              })
            )
          );

          return result;
        });

        // Send line items to the UI
        dataStream.writeData({
          type: 'invoice-line-items',
          content: calculatedLineItems.map((item, index) => ({
            id: `temp-${index}`, // We don't have the real IDs yet
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            amount: item.totalPrice,
          })),
        });

        // Send total amount to the UI
        dataStream.writeData({
          type: 'invoice-total',
          content: {
            amount: totalAmount,
            currency,
          },
        });

        // Signal completion
        dataStream.writeData({
          type: isEditing ? 'invoice-editing-complete' : 'invoice-creation-complete',
          content: {
            id: invoice.id,
            number: invoiceNumber,
          },
        });

        // Get the created/updated invoice with line items
        const finalInvoice = await db.invoice.findUnique({
          where: { id: invoice.id },
          include: { lineItems: true, category: true },
        });

        if (!finalInvoice) {
          throw new Error('Failed to retrieve the invoice');
        }

        return {
          id: finalInvoice.id,
          number: finalInvoice.invoiceNumber || invoiceNumber,
          vendorName: finalInvoice.vendorName || vendorName,
          amount: finalInvoice.amount || totalAmount,
          currency: finalInvoice.currency || currency,
          status: finalInvoice.status,
          category: finalInvoice.category?.name,
          isEdited: isEditing
        };
      } catch (error) {
        // Signal error to the UI
        dataStream.writeData({
          type: 'invoice-creation-error',
          content: {
            message: 'Failed to create invoice. Please try again.',
            error: error instanceof Error ? error.message : String(error)
          },
        });

        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    },
  });

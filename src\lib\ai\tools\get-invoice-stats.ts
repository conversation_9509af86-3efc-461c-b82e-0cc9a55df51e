import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const getInvoiceStats = tool({
  description: 'Computes invoice counts and total amounts by status (paid, pending, overdue) and overall totals for the user.',
  parameters: z.object({
    startDate: z.string().optional().describe('Optional start date for the period to analyze (YYYY-MM-DD)'),
    endDate: z.string().optional().describe('Optional end date for the period to analyze (YYYY-MM-DD)'),
    currency: z.string().optional().describe('Optional currency to filter by (e.g., USD, EUR)'),
  }),
  execute: async ({ startDate, endDate, currency }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Build the where clause for the query
      const where: Record<string, unknown> = {
        userId,
      };

      // Add date filters if provided - use createdAt instead of issueDate
      if (startDate) {
        where.createdAt = {
          ...(where.createdAt || {}),
          gte: new Date(startDate),
        };
      }

      if (endDate) {
        where.createdAt = {
          ...(where.createdAt || {}),
          lte: new Date(endDate),
        };
      }

      // Add currency filter if provided
      if (currency) {
        where.currency = currency;
      }

      // Get all invoices for the user with the applied filters
      const invoices = await db.invoice.findMany({
        where,
        select: {
          id: true,
          amount: true,
          currency: true,
          status: true,
          issueDate: true,
          dueDate: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // Initialize stats object
      const stats = {
        total: {
          count: 0,
          amount: 0,
        },
        paid: {
          count: 0,
          amount: 0,
        },
        pending: {
          count: 0,
          amount: 0,
        },
        overdue: {
          count: 0,
          amount: 0,
        },
        cancelled: {
          count: 0,
          amount: 0,
        },
        currencyBreakdown: {} as Record<string, { count: number; amount: number }>,
        timeframe: {
          startDate: startDate ? new Date(startDate).toISOString() : null,
          endDate: endDate ? new Date(endDate).toISOString() : null,
        },
      };

      // Calculate stats
      for (const invoice of invoices) {
        // Update total stats
        stats.total.count++;
        stats.total.amount += invoice.amount || 0;

        // Update status-specific stats
        switch (invoice.status) {
          case 'PAID':
            stats.paid.count++;
            stats.paid.amount += invoice.amount || 0;
            break;
          case 'PENDING':
            stats.pending.count++;
            stats.pending.amount += invoice.amount || 0;
            break;
          case 'OVERDUE':
            stats.overdue.count++;
            stats.overdue.amount += invoice.amount || 0;
            break;
          case 'CANCELLED':
            stats.cancelled.count++;
            stats.cancelled.amount += invoice.amount || 0;
            break;
        }

        // Update currency breakdown
        const currencyKey = invoice.currency || 'UNKNOWN';
        if (!stats.currencyBreakdown[currencyKey]) {
          stats.currencyBreakdown[currencyKey] = { count: 0, amount: 0 };
        }
        stats.currencyBreakdown[currencyKey].count++;
        stats.currencyBreakdown[currencyKey].amount += invoice.amount || 0;
      }

      // Calculate percentages
      const result = {
        ...stats,
        percentages: {
          paid: stats.total.count > 0 ? (stats.paid.count / stats.total.count) * 100 : 0,
          pending: stats.total.count > 0 ? (stats.pending.count / stats.total.count) * 100 : 0,
          overdue: stats.total.count > 0 ? (stats.overdue.count / stats.total.count) * 100 : 0,
          cancelled: stats.total.count > 0 ? (stats.cancelled.count / stats.total.count) * 100 : 0,
        },
        systemDate: {
          current: new Date().toISOString(),
          currentFormatted: new Date().toISOString().split('T')[0],
          year: new Date().getFullYear(),
          month: new Date().getMonth() + 1, // JavaScript months are 0-indexed
          day: new Date().getDate()
        },
        message: "Note: These statistics are based on invoice creation dates (when they were added to the system), not the issue dates shown on the invoices."
      };

      return result;
    } catch (error) {
      return {
        success: false,
        message: 'Failed to retrieve invoice statistics. Please try again.',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

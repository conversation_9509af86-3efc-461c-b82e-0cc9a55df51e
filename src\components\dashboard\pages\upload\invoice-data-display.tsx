import { useState, useEffect } from 'react';
import type { InvoiceData } from '@/types/invoice';
import { flattenObjectsToStrings } from '@/utils/object-to-string';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Plus,
  Globe,
  Clock,
  Briefcase,
  Building,
  CheckCircle,
  FileUp,
  X,
  Pencil,
  AlertTriangle,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { InvoiceCategorySelector } from './invoice-category-selector';
import { InvoiceDetailsSection } from './invoice-details-section';
import { InvoiceLineItems } from './invoice-line-items';
import { toast } from 'sonner';

interface InvoiceDataDisplayProps {
  data: InvoiceData & { id?: string };
  onUpdate: (data: InvoiceData & { id?: string }) => void;
}

export function InvoiceDataDisplay({
  data,
  onUpdate,
}: InvoiceDataDisplayProps) {
  // Process the data to handle nested objects
  const processedData = flattenObjectsToStrings(
    data
  ) as InvoiceData & { id?: string };

  // Ensure lineItems is always an array
  if (!Array.isArray(processedData.lineItems)) {
    processedData.lineItems = [];
  }

  const [isEditing, setIsEditing] = useState(false);
  const [editedData, setEditedData] =
    useState<InvoiceData>(processedData);
  const [expandedSections, setExpandedSections] = useState<string[]>([
    'invoice-details',
    'meta-info',
  ]);
  const [addSectionDialog, setAddSectionDialog] = useState(false);
  const [addFieldDialog, setAddFieldDialog] = useState(false);
  const [selectedSection, setSelectedSection] = useState<string>('');
  const [newFieldName, setNewFieldName] = useState('');
  const [newFieldValue, setNewFieldValue] = useState('');

  // Update editedData when data changes
  useEffect(() => {
    const processed = flattenObjectsToStrings(data) as InvoiceData & {
      id?: string;
    };

    // Ensure lineItems is always an array
    if (!Array.isArray(processed.lineItems)) {
      processed.lineItems = [];
    }

    setEditedData(processed);
  }, [data]);

  // State for UI interactions

  const handleInputChange = (
    section: string,
    field: string,
    value: string
  ) => {
    setEditedData((prev) => {
      const newData = { ...prev };

      // Handle nested paths like "vendor.name" or "financials.subtotal"
      const parts = field.split('.');

      if (parts.length === 1) {
        // Simple field at the top level
        newData[field] = value;
      } else if (parts.length === 2) {
        // One level of nesting
        const [section, key] = parts;

        // Handle specific known sections with proper types
        if (section === 'vendor' || section === 'customer') {
          newData[section] = {
            ...(newData[section] || {}),
            [key]: value,
          };
        } else if (section === 'financials') {
          newData.financials = {
            ...(newData.financials || {}),
            [key]: value,
          };
        } else if (section === 'payment') {
          newData.payment = {
            ...(newData.payment || {}),
            [key]: value,
          };
        } else if (section === 'additionalFields') {
          if (!newData.additionalFields)
            newData.additionalFields = {};
          newData.additionalFields[key] = value;
        } else {
          // For other sections, create a generic Record
          (newData as Record<string, Record<string, unknown>>)[
            section
          ] = {
            ...((newData as Record<string, Record<string, unknown>>)[
              section
            ] || {}),
            [key]: value,
          };
        }
      } else if (parts.length === 3 && parts[0] === 'lineItems') {
        // Line items with index
        const [, indexStr, key] = parts;
        const index = Number.parseInt(indexStr, 10);
        if (Array.isArray(newData.lineItems)) {
          const lineItems = [...newData.lineItems];
          if (!lineItems[index]) {
            lineItems[index] = {};
          }
          lineItems[index] = { ...lineItems[index], [key]: value };
          newData.lineItems = lineItems;
        }
      } else if (parts.length === 3) {
        // Two levels of nesting
        const [sectionName, subsection, key] = parts;
        // Ensure the section exists
        if (
          !newData[sectionName] ||
          typeof newData[sectionName] !== 'object'
        ) {
          newData[sectionName] = {};
        }
        // Ensure the subsection exists
        const section = newData[sectionName] as Record<
          string,
          unknown
        >;
        if (
          !section[subsection] ||
          typeof section[subsection] !== 'object'
        ) {
          section[subsection] = {};
        }
        // Set the value
        (section[subsection] as Record<string, string>)[key] = value;
      }

      return newData;
    });
  };

  const handleSave = () => {
    // Process any nested objects before saving
    const processedData = flattenObjectsToStrings(
      editedData
    ) as InvoiceData & { id?: string };
    onUpdate(processedData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedData(data);
    setIsEditing(false);
  };

  // Helper function to render nested object fields
  function renderObjectFields(
    obj: Record<string, unknown>,
    path: string,
    isEditing: boolean,
    handleInputChange: (
      section: string,
      field: string,
      value: string
    ) => void
  ) {
    return Object.entries(obj).map(([key, value]) => {
      const fieldPath = path ? `${path}.${key}` : key;
      if (
        typeof value === 'object' &&
        value !== null &&
        !Array.isArray(value)
      ) {
        return (
          <div key={fieldPath} className="ml-4">
            <label className="text-xs font-medium capitalize text-muted-foreground">
              {key.replace(/([A-Z])/g, ' $1').trim()}
            </label>
            {renderObjectFields(
              value as Record<string, unknown>,
              fieldPath,
              isEditing,
              handleInputChange
            )}
          </div>
        );
      } else if (Array.isArray(value)) {
        // For arrays, display as JSON string (or customize as needed)
        return (
          <div key={fieldPath} className="ml-4">
            <label className="text-xs font-medium capitalize text-muted-foreground">
              {key.replace(/([A-Z])/g, ' $1').trim()}
            </label>
            <p className="text-sm bg-slate-50 dark:bg-slate-900 p-2 rounded border border-slate-200 dark:border-slate-800">
              {JSON.stringify(value)}
            </p>
          </div>
        );
      } else if (
        value !== undefined &&
        value !== null &&
        value !== ''
      ) {
        return (
          <div key={fieldPath} className="space-y-2">
            <label className="text-sm font-medium capitalize">
              {key.replace(/([A-Z])/g, ' $1').trim()}
            </label>
            {isEditing ? (
              String(value).length > 50 ? (
                <Textarea
                  value={String(value)}
                  onChange={(e) =>
                    handleInputChange(path, fieldPath, e.target.value)
                  }
                  rows={2}
                />
              ) : (
                <Input
                  value={String(value)}
                  onChange={(e) =>
                    handleInputChange(path, fieldPath, e.target.value)
                  }
                />
              )
            ) : (
              <p className="text-sm bg-slate-50 dark:bg-slate-900 p-2 rounded border border-slate-200 dark:border-slate-800">
                {String(value)}
              </p>
            )}
          </div>
        );
      }
      return null;
    });
  }

  // Replace the renderFields function with the following:
  const renderFields = (
    obj: Record<string, unknown>,
    path: string
  ) => {
    if (!obj) return null;
    return renderObjectFields(
      obj,
      path,
      isEditing,
      handleInputChange
    );
  };

  // Get all sections from the data
  const sections = Object.keys(data).filter(
    (key) =>
      typeof data[key] === 'object' &&
      !Array.isArray(data[key]) &&
      key !== 'lineItems' &&
      key !== 'additionalFields' &&
      key !== 'meta'
  );

  // Function to format processing time
  const formatProcessingTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  // Function to handle category or vendor type selection
  const handleSuggestionSelect = (
    type: 'category' | 'vendorType',
    value: string
  ) => {
    try {
      // Create a deep copy to avoid reference issues
      const updatedData = JSON.parse(JSON.stringify(editedData));

      // Ensure meta exists
      if (!updatedData.meta) {
        updatedData.meta = {};
      }

      // Handle case where meta is a string
      if (typeof updatedData.meta === 'string') {
        try {
          updatedData.meta = JSON.parse(updatedData.meta);
        } catch {
          updatedData.meta = {};
        }
      }

      // Ensure suggestions exists
      if (!updatedData.meta.suggestions) {
        updatedData.meta.suggestions = {};
      }

      // Handle case where suggestions is a string
      if (typeof updatedData.meta.suggestions === 'string') {
        try {
          updatedData.meta.suggestions = JSON.parse(
            updatedData.meta.suggestions
          );
        } catch {
          updatedData.meta.suggestions = {};
        }
      }

      // Now we can safely update the property
      if (type === 'category') {
        updatedData.meta.suggestions.selectedCategory = value;

        // Update the DOM element directly to ensure it reflects the current selection
        // Use a more specific selector that includes the invoice ID to ensure we're updating the correct element
        const categoryElement =
          document.querySelector(
            `[data-selected-category][data-invoice-id="${data.id}"]`
          ) || document.querySelector('[data-selected-category]');
        if (categoryElement) {
          categoryElement.setAttribute('data-value', value);
          categoryElement.setAttribute(
            'data-selected-category',
            value
          );
          categoryElement.setAttribute(
            'data-invoice-id',
            data.id || ''
          );
        }
      } else {
        updatedData.meta.suggestions.selectedVendorType = value;

        // Update the DOM element directly to ensure it reflects the current selection
        // Use a more specific selector that includes the invoice ID to ensure we're updating the correct element
        const vendorElement =
          document.querySelector(
            `[data-selected-vendor-type][data-invoice-id="${data.id}"]`
          ) || document.querySelector('[data-selected-vendor-type]');
        if (vendorElement) {
          vendorElement.setAttribute('data-value', value);
          vendorElement.setAttribute(
            'data-selected-vendor-type',
            value
          );
          vendorElement.setAttribute(
            'data-invoice-id',
            data.id || ''
          );
        }
      }

      // Update local state
      setEditedData(updatedData);

      // Important: Also update the parent component with the new data
      // This ensures the parent has the latest data when saving
      // Don't use flattenObjectsToStrings here to preserve the object structure
      onUpdate(updatedData);

      // Force a re-render by updating a state variable
      setExpandedSections([...expandedSections]);

      // The actual saving is now handled by the InvoiceCategorySelector component
      // We don't need to call the API here anymore as it's done automatically when a selection is made
    } catch {
      toast.error("Failed to handle suggestion select");
    }
  };

  // Function to add a new section to the invoice data
  const addSection = (sectionType: string) => {
    setEditedData((prev) => {
      const newData = { ...prev };

      switch (sectionType) {
        case 'vendor':
          newData.vendor = newData.vendor || {};
          break;
        case 'customer':
          newData.customer = newData.customer || {};
          break;
        case 'financials':
          newData.financials = newData.financials || {};
          break;
        case 'payment':
          newData.payment = newData.payment || {};
          break;
        case 'additionalFields':
          newData.additionalFields = newData.additionalFields || {};
          break;
      }

      return newData;
    });

    // Close the dialog
    setAddSectionDialog(false);

    // Expand the new section
    setExpandedSections((prev) => [...prev, sectionType]);
  };

  // Function to add a new field to a section
  const addField = () => {
    if (!newFieldName.trim()) return;

    setEditedData((prev) => {
      const newData = { ...prev };

      if (selectedSection === '') {
        // Add field to top level
        newData[newFieldName] = newFieldValue;
      } else if (selectedSection === 'additionalFields') {
        // Add to additionalFields
        newData.additionalFields = newData.additionalFields || {};
        newData.additionalFields[newFieldName] = newFieldValue;
      } else {
        // Add to a specific section
        newData[selectedSection] = newData[selectedSection] || {};
        (newData[selectedSection] as Record<string, string>)[
          newFieldName
        ] = newFieldValue;
      }

      return newData;
    });

    // Reset and close dialog
    setNewFieldName('');
    setNewFieldValue('');
    setAddFieldDialog(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-900/50 dark:to-gray-900/50 p-4 rounded-lg border border-slate-100 dark:border-slate-800 shadow-sm">
        <div className="flex items-center">
          <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
            <FileUp className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold">Invoice Details</h2>
            <p className="text-sm text-muted-foreground">
              Review and manage extracted invoice information
            </p>
          </div>
          {data.id && (
            <Badge
              variant="outline"
              className="ml-3 bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800/50"
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Saved to Database
            </Badge>
          )}
        </div>
        <div className="flex flex-wrap gap-2 mt-2 md:mt-0">
          {isEditing ? (
            <>
              <Button
                onClick={handleSave}
                variant="default"
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
              <Button
                onClick={() => setAddFieldDialog(true)}
                variant="outline"
                className="border-slate-200 dark:border-slate-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Field
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                className="border-slate-200 dark:border-slate-700"
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </>
          ) : (
            <>
              <Button
                onClick={() => setIsEditing(true)}
                variant="outline"
                className="border-slate-200 dark:border-slate-700"
              >
                <Pencil className="h-4 w-4 mr-2" />
                Edit Data
              </Button>
              <Button
                onClick={() => setAddSectionDialog(true)}
                variant="outline"
                className="border-slate-200 dark:border-slate-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Section
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Add missing fields based on what's available in the invoice data type */}
      {isEditing && (
        <div className="grid grid-cols-2 gap-4 p-4 border rounded-lg mb-4 bg-muted/10">
          <div className="col-span-2">
            <h3 className="text-sm font-medium mb-2">
              Add Common Fields
            </h3>
          </div>
          {!editedData.referenceCode && (
            <Button
              size="sm"
              variant="outline"
              onClick={() =>
                handleInputChange('', 'referenceCode', '')
              }
              className="justify-start"
            >
              <Plus className="h-4 w-4 mr-2" />
              Reference Code
            </Button>
          )}
          {!editedData.poNumber && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleInputChange('', 'poNumber', '')}
              className="justify-start"
            >
              <Plus className="h-4 w-4 mr-2" />
              PO Number
            </Button>
          )}
          {!editedData.orderNumber && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleInputChange('', 'orderNumber', '')}
              className="justify-start"
            >
              <Plus className="h-4 w-4 mr-2" />
              Order Number
            </Button>
          )}
          {!editedData.dueDate && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleInputChange('', 'dueDate', '')}
              className="justify-start"
            >
              <Plus className="h-4 w-4 mr-2" />
              Due Date
            </Button>
          )}
        </div>
      )}

      <Accordion
        type="multiple"
        value={expandedSections}
        onValueChange={setExpandedSections}
        className="w-full"
      >
        {/* Basic Invoice Details */}
        <AccordionItem
          value="invoice-details"
          className="border rounded-lg mb-4 overflow-hidden"
        >
          <AccordionTrigger className="px-4 py-3 hover:bg-muted/50 hover:no-underline">
            Invoice Details
          </AccordionTrigger>
          <AccordionContent className="px-0 pt-0">
            <Card className="border-0 shadow-none">
              <CardContent className="p-4">
                {/* Enhanced Invoice Details Section */}
                <InvoiceDetailsSection
                  data={data}
                  editedData={editedData}
                  isEditing={isEditing}
                  onInputChange={handleInputChange}
                />
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        {/* Meta Information */}
        {data.meta && (
          <AccordionItem
            value="meta-info"
            className="border rounded-lg mb-4 overflow-hidden"
          >
            <AccordionTrigger className="px-4 py-3 hover:bg-muted/50 hover:no-underline">
              Document Information
            </AccordionTrigger>
            <AccordionContent className="px-0 pt-0">
              <Card className="border-0 shadow-none">
                <CardContent className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Language */}
                    {data.meta.language && (
                      <div className="flex flex-col p-4 bg-muted/30 dark:bg-muted/10 rounded-lg">
                        <div className="flex items-center mb-2">
                          <Globe className="h-4 w-4 mr-2 text-blue-500" />
                          <span className="font-medium">
                            Language
                          </span>
                        </div>
                        <div className="flex items-center">
                          <span className="text-lg font-semibold">
                            {data.meta.languageName ||
                              data.meta.language}
                          </span>
                          <span className="text-xs ml-2 text-muted-foreground">
                            ({data.meta.language})
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Country */}
                    {data.meta.country && (
                      <div className="flex flex-col p-4 bg-muted/30 dark:bg-muted/10 rounded-lg">
                        <div className="flex items-center mb-2">
                          <Globe className="h-4 w-4 mr-2 text-green-500" />
                          <span className="font-medium">Country</span>
                        </div>
                        <div className="flex items-center">
                          <span className="text-lg font-semibold">
                            {data.meta.country}
                          </span>
                          {data.meta.countryCode && (
                            <span className="text-xs ml-2 text-muted-foreground">
                              ({data.meta.countryCode})
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Processing Time */}
                    {data.meta.processingTime && (
                      <div className="flex flex-col p-4 bg-muted/30 dark:bg-muted/10 rounded-lg">
                        <div className="flex items-center mb-2">
                          <Clock className="h-4 w-4 mr-2 text-amber-500" />
                          <span className="font-medium">
                            Processing Time
                          </span>
                        </div>
                        <span className="text-lg font-semibold">
                          {formatProcessingTime(
                            data.meta.processingTime
                          )}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Category & Vendor Type Suggestions */}
                  <InvoiceCategorySelector
                    data={data}
                    editedData={editedData}
                    onSelect={handleSuggestionSelect}
                    onUpdate={onUpdate}
                  />

                  {/* Backend Parsing Issue Warning */}
                  {data.meta?.backendParsingIssue && (
                    <div className="mb-6 p-4 bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-900/50 rounded-lg">
                      <div className="flex items-start">
                        <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400 mr-3 flex-shrink-0 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-amber-800 dark:text-amber-200 mb-1">
                            Backend Processing Issue Detected
                          </h4>
                          <p className="text-sm text-amber-700 dark:text-amber-300 mb-2">
                            The backend AI had difficulty parsing this
                            invoice&apos;s structure, particularly with
                            line items.
                            {data.meta.recoveredFromError &&
                              ' We recovered some data from the malformed response.'}
                            {data.meta.recoveredFromRegex &&
                              ' We extracted data using backup parsing methods.'}
                          </p>
                          <p className="text-xs text-amber-600 dark:text-amber-400">
                            💡 This often happens with complex line
                            item tables. The basic invoice data should
                            still be accurate, but line items may be
                            incomplete or missing. Consider
                            re-uploading if critical line item data is
                            missing.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Confidence Score */}
                  {data.meta.confidence?.overall !== undefined && (
                    <div className="mt-6">
                      <div className="flex justify-between items-end mb-2">
                        <h3 className="font-medium">
                          Extraction Confidence
                        </h3>
                        <span
                          className={`text-lg font-bold ${
                            data.meta.confidence.overall >= 90
                              ? 'text-green-600 dark:text-green-400'
                              : data.meta.confidence.overall >= 75
                                ? 'text-amber-600 dark:text-amber-400'
                                : 'text-red-600 dark:text-red-400'
                          }`}
                        >
                          {String(data.meta.confidence.overall)}%
                        </span>
                      </div>
                      <div className="h-3 w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                        <div
                          className={`h-full ${
                            data.meta.confidence.overall >= 90
                              ? 'bg-green-500 dark:bg-green-500/70'
                              : data.meta.confidence.overall >= 75
                                ? 'bg-amber-500 dark:bg-amber-500/70'
                                : 'bg-red-500 dark:bg-red-500/70'
                          }`}
                          style={{
                            width: `${data.meta.confidence.overall}%`,
                          }}
                        />
                      </div>

                      {/* Field-level confidence scores */}
                      {data.meta.confidence.fields &&
                        Object.keys(data.meta.confidence.fields)
                          .length > 0 && (
                          <div className="mt-4 space-y-2">
                            <details>
                              <summary className="text-sm font-medium cursor-pointer">
                                Field-level confidence scores
                              </summary>
                              <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-2 pt-2">
                                {Object.entries(
                                  data.meta.confidence.fields
                                ).map(([field, score]) => {
                                  // Handle case where score is an object (like {GST: 90, PST: 85})
                                  if (
                                    typeof score === 'object' &&
                                    score !== null
                                  ) {
                                    return (
                                      <div
                                        key={field}
                                        className="flex flex-col space-y-1"
                                      >
                                        <span className="capitalize font-medium">
                                          {field
                                            .replace(
                                              /([A-Z])/g,
                                              ' $1'
                                            )
                                            .trim()}
                                        </span>
                                        {Object.entries(score).map(
                                          ([subField, subScore]) => {
                                            const scoreValue =
                                              Number(subScore);
                                            return (
                                              <div
                                                key={`${field}-${subField}`}
                                                className="flex justify-between items-center text-sm pl-4"
                                              >
                                                <span className="capitalize">
                                                  {subField}
                                                </span>
                                                <span
                                                  className={`font-semibold ${
                                                    scoreValue >= 90
                                                      ? 'text-green-600 dark:text-green-400'
                                                      : scoreValue >=
                                                          75
                                                        ? 'text-amber-600 dark:text-amber-400'
                                                        : 'text-red-600 dark:text-red-400'
                                                  }`}
                                                >
                                                  {String(subScore)}%
                                                </span>
                                              </div>
                                            );
                                          }
                                        )}
                                      </div>
                                    );
                                  }

                                  // Handle normal number score
                                  return (
                                    <div
                                      key={field}
                                      className="flex justify-between items-center text-sm"
                                    >
                                      <span className="capitalize">
                                        {field
                                          .replace(/([A-Z])/g, ' $1')
                                          .trim()}
                                      </span>
                                      <span
                                        className={`font-semibold ${
                                          Number(score) >= 90
                                            ? 'text-green-600 dark:text-green-400'
                                            : Number(score) >= 75
                                              ? 'text-amber-600 dark:text-amber-400'
                                              : 'text-red-600 dark:text-red-400'
                                        }`}
                                      >
                                        {String(score)}%
                                      </span>
                                    </div>
                                  );
                                })}
                              </div>
                            </details>
                          </div>
                        )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </AccordionContent>
          </AccordionItem>
        )}

        {/* Dynamic Sections */}
        {sections.map((section) => (
          <AccordionItem
            key={section}
            value={section}
            className="border rounded-lg mb-4 overflow-hidden"
          >
            <AccordionTrigger className="px-4 py-3 hover:bg-muted/50 hover:no-underline capitalize">
              {section.replace(/([A-Z])/g, ' $1').trim()} Information
            </AccordionTrigger>
            <AccordionContent className="px-0 pt-0">
              <Card className="border-0 shadow-none">
                <CardContent className="p-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    {data[section] &&
                    typeof data[section] === 'object'
                      ? renderFields(
                          data[section] as Record<string, unknown>,
                          section
                        )
                      : null}
                    {isEditing && (
                      <div className="col-span-2 mt-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedSection(section);
                            setAddFieldDialog(true);
                          }}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Field
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </AccordionContent>
          </AccordionItem>
        ))}

        {/* Line Items */}
        <AccordionItem
          value="line-items"
          className="border rounded-lg mb-4 overflow-hidden"
        >
          <AccordionTrigger className="px-4 py-3 hover:bg-muted/50 hover:no-underline">
            Line Items
          </AccordionTrigger>
          <AccordionContent className="px-0 pt-0">
            <Card className="border-0 shadow-none">
              <CardContent className="p-4 overflow-x-auto">
                <InvoiceLineItems
                  data={data}
                  editedData={editedData}
                  isEditing={isEditing}
                  onInputChange={handleInputChange}
                  onUpdate={onUpdate}
                />
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        {/* Additional Fields */}
        {data.additionalFields &&
          Object.keys(data.additionalFields).length > 0 && (
            <AccordionItem
              value="additional-fields"
              className="border rounded-lg mb-4 overflow-hidden"
            >
              <AccordionTrigger className="px-4 py-3 hover:bg-muted/50 hover:no-underline">
                Additional Fields
              </AccordionTrigger>
              <AccordionContent className="px-0 pt-0">
                <Card className="border-0 shadow-none">
                  <CardContent className="p-4">
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      {renderFields(
                        data.additionalFields,
                        'additionalFields'
                      )}
                    </div>
                  </CardContent>
                </Card>
              </AccordionContent>
            </AccordionItem>
          )}

        {/* Notes and Terms */}
        {(data.notes || data.termsAndConditions) && (
          <AccordionItem
            value="notes-terms"
            className="border rounded-lg mb-4 overflow-hidden"
          >
            <AccordionTrigger className="px-4 py-3 hover:bg-muted/50 hover:no-underline">
              Notes and Terms
            </AccordionTrigger>
            <AccordionContent className="px-0 pt-0">
              <Card className="border-0 shadow-none">
                <CardContent className="p-4">
                  <div className="space-y-6">
                    {data.notes && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Notes
                        </label>
                        {isEditing ? (
                          <Textarea
                            value={editedData.notes || ''}
                            onChange={(e) =>
                              handleInputChange(
                                '',
                                'notes',
                                e.target.value
                              )
                            }
                            rows={3}
                          />
                        ) : (
                          <p className="whitespace-pre-line">
                            {data.notes}
                          </p>
                        )}
                      </div>
                    )}
                    {data.termsAndConditions && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Terms and Conditions
                        </label>
                        {isEditing ? (
                          <Textarea
                            value={
                              editedData.termsAndConditions || ''
                            }
                            onChange={(e) =>
                              handleInputChange(
                                '',
                                'termsAndConditions',
                                e.target.value
                              )
                            }
                            rows={3}
                          />
                        ) : (
                          <p className="whitespace-pre-line">
                            {data.termsAndConditions}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </AccordionContent>
          </AccordionItem>
        )}
      </Accordion>

      {/* Add Section Dialog */}
      <Dialog
        open={addSectionDialog}
        onOpenChange={setAddSectionDialog}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Section</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {!editedData.vendor && (
              <Button
                onClick={() => addSection('vendor')}
                variant="outline"
              >
                <Building className="mr-2 h-4 w-4" />
                Add Vendor Information
              </Button>
            )}
            {!editedData.customer && (
              <Button
                onClick={() => addSection('customer')}
                variant="outline"
              >
                <Briefcase className="mr-2 h-4 w-4" />
                Add Customer Information
              </Button>
            )}
            {!editedData.financials && (
              <Button
                onClick={() => addSection('financials')}
                variant="outline"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Financial Information
              </Button>
            )}
            {!editedData.payment && (
              <Button
                onClick={() => addSection('payment')}
                variant="outline"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Payment Information
              </Button>
            )}
            {!editedData.additionalFields && (
              <Button
                onClick={() => addSection('additionalFields')}
                variant="outline"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Additional Fields
              </Button>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Add Field Dialog */}
      <Dialog open={addFieldDialog} onOpenChange={setAddFieldDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Field</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Field Name
              </label>
              <Input
                value={newFieldName}
                onChange={(e) => setNewFieldName(e.target.value)}
                placeholder="Enter field name"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Field Value
              </label>
              <Input
                value={newFieldValue}
                onChange={(e) => setNewFieldValue(e.target.value)}
                placeholder="Enter field value"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              onClick={addField}
              disabled={!newFieldName.trim()}
            >
              Add Field
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

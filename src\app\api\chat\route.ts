import {
  type Message,
  createDataStreamResponse,
  streamText,
} from 'ai';
import { myProvider } from '@/lib/ai/models';
import { systemPrompt } from '@/lib/ai/prompts';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import {
  generateUUID,
  getMostRecentUserMessage,
  sanitizeResponseMessages,
} from '@/lib/utils';

import { generateTitleFromUserMessage } from '@/app/dashboard/chat/actions';
// Only essential tools to reduce bundle size and token usage
import { listInvoices } from '@/lib/ai/tools/list-invoices';
import { getInvoiceDetails } from '@/lib/ai/tools/get-invoice-details';
import { createInvoice } from '@/lib/ai/tools/create-invoice';
import { updateInvoiceStatus } from '@/lib/ai/tools/update-invoice-status';
import { getInvoiceStats } from '@/lib/ai/tools/get-invoice-stats';
import { generateReport } from '@/lib/ai/tools/generate-report';
import db from '@/db/db';

export const maxDuration = 60;

export async function POST(request: Request) {
  const {
    id,
    messages,
    selectedChatModel,
  }: {
    id: string;
    messages: Array<Message>;
    selectedChatModel: string;
  } = await request.json();

  const userId = await getCurrentUserId();

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Check chat usage limits before processing
  const { checkCurrentUserChatUsage, incrementCurrentUserChatUsage } =
    await import('@/lib/services/usage-service');

  const usageCheck = await checkCurrentUserChatUsage();

  if (!usageCheck.allowed) {
    return new Response(usageCheck.message || 'Chat limit exceeded', {
      status: 429,
    });
  }

  const userMessage = getMostRecentUserMessage(messages);

  if (!userMessage) {
    return new Response('No user message found', { status: 400 });
  }

  // Check if chat exists
  const chat = await db.chat.findUnique({
    where: { id },
  });

  // If chat doesn't exist, create it
  if (!chat) {
    const title = await generateTitleFromUserMessage({
      message: userMessage,
    });
    await db.chat.create({
      data: {
        id,
        userId,
        title,
        createdAt: new Date(),
      },
    });
  }

  // Check if the message already exists (for edited messages)
  const existingMessage = await db.message.findUnique({
    where: { id: userMessage.id },
  });

  if (existingMessage) {
    // Update the existing message
    await db.message.update({
      where: { id: userMessage.id },
      data: {
        content: userMessage.content,
      },
    });
  } else {
    // Create a new message
    await db.message.create({
      data: {
        id: userMessage.id,
        chatId: id,
        role: userMessage.role,
        content: userMessage.content,
        createdAt: new Date(),
      },
    });
  }

  // --- AGGRESSIVE OPTIMIZATION FOR TOKEN LIMITS ---
  const modelToUse = 'billix-chat';
  const maxSteps = 2; // Reduced to prevent repetitive tool calls
  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), 8000); // Shorter timeout

  // AGGRESSIVE MESSAGE TRUNCATION - Keep only 3 most recent messages
  const truncateMessages = (msgs: Array<Message>) => {
    // Keep only the last 3 messages to stay well under token limits
    return msgs.slice(-3);
  };

  const truncatedMessages = truncateMessages(messages);

  // --- OPTIMIZATION END ---

  try {
    return createDataStreamResponse({
      execute: (dataStream) => {
        const result = streamText({
          model: myProvider.languageModel(modelToUse),
          system: systemPrompt({ selectedChatModel: modelToUse }),
          messages: truncatedMessages,
          maxSteps,
          experimental_activeTools: [
            // Essential tools only to reduce token usage
            'listInvoices',
            'getInvoiceDetails',
            'createInvoice',
            'updateInvoiceStatus',
            'getInvoiceStats',
            'generateReport',
          ],
          experimental_generateMessageId: generateUUID,

          tools: {
            // Only essential tools to reduce token usage
            listInvoices,
            getInvoiceDetails,
            createInvoice: createInvoice({
              userId: userId || '',
              dataStream,
            }),
            updateInvoiceStatus,
            getInvoiceStats,
            generateReport: generateReport({
              userId: userId || '',
              dataStream,
            }),
          },
          onFinish: async ({ response, reasoning }) => {
            console.log(`✅ [BILLIX AI] Stream finished for chat ${id}`);
            console.timeEnd(`billix-response-${id}`);
            
            if (userId) {
              try {
                console.log(`💾 [BILLIX AI] Saving ${response.messages.length} response messages`);
                
                const sanitizedResponseMessages =
                  sanitizeResponseMessages({
                    messages: response.messages,
                    reasoning,
                  });

                console.log(`🧹 [BILLIX AI] Sanitized to ${sanitizedResponseMessages.length} messages`);

                // Save all response messages
                await db.message.createMany({
                  data: sanitizedResponseMessages.map((message) => {
                    return {
                      id: message.id,
                      chatId: id,
                      role: message.role,
                      content: JSON.stringify(message.content),
                      createdAt: new Date(),
                    };
                  }),
                });

                // Increment chat usage counter after successful response
                await incrementCurrentUserChatUsage();
              } catch (error) {
                console.error(`❌ [BILLIX AI] Failed to save chat messages:`, error);
              }
            }
          },
          experimental_telemetry: {
            isEnabled: true,
            functionId: 'billix-ai-chat',
          },
          abortSignal: controller.signal,
        });

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: false,
        });
      },
      onError: (error) => {
        console.error(`💥 [BILLIX AI] Error:`, error);
        return 'Something went wrong! Please try again.';
      },
    });
  } catch (error) {
    console.error(`🚨 [BILLIX AI] Critical error:`, error);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: 'Something went wrong. Please try again!'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  } finally {
    clearTimeout(timeout);
  }
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('Not Found', { status: 404 });
  }

  const userId = await getCurrentUserId();

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const chat = await db.chat.findUnique({
      where: { id },
    });

    if (!chat) {
      return new Response('Chat not found', { status: 404 });
    }

    if (chat.userId !== userId) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Delete the chat (cascade will delete messages and votes)
    await db.chat.delete({
      where: { id },
    });

    return new Response('Chat deleted', { status: 200 });
  } catch (error) {
    console.error('Error deleting chat:', error);
    return new Response(
      'An error occurred while processing your request',
      {
        status: 500,
      }
    );
  }
}

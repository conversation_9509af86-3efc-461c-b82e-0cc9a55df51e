import {
  type Message,
  createDataStreamResponse,
  smoothStream,
  streamText,
} from 'ai';
import { myProvider } from '@/lib/ai/models';
import { systemPrompt } from '@/lib/ai/prompts';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import {
  generateUUID,
  getMostRecentUserMessage,
  sanitizeResponseMessages,
} from '@/lib/utils';

import { generateTitleFromUserMessage } from '@/app/dashboard/chat/actions';
import { createDocument } from '@/lib/ai/tools/create-document';
import { updateDocument } from '@/lib/ai/tools/update-document';
import { requestSuggestions } from '@/lib/ai/tools/request-suggestions';
import { getWeather } from '@/lib/ai/tools/get-weather';
import { listInvoices } from '@/lib/ai/tools/list-invoices';
import { getInvoiceDetails } from '@/lib/ai/tools/get-invoice-details';
import { createInvoice } from '@/lib/ai/tools/create-invoice';
import { updateInvoiceStatus } from '@/lib/ai/tools/update-invoice-status';
import { deleteInvoice } from '@/lib/ai/tools/delete-invoice';
import { categorizeInvoice } from '@/lib/ai/tools/categorize-invoice';
import { createFolder } from '@/lib/ai/tools/create-folder';
import { listFolders } from '@/lib/ai/tools/list-folders';
import { addInvoiceToFolder } from '@/lib/ai/tools/add-invoice-to-folder';
import { removeInvoiceFromFolder } from '@/lib/ai/tools/remove-invoice-from-folder';
import { getInvoiceStats } from '@/lib/ai/tools/get-invoice-stats';
import { getVendorSpendAnalysis } from '@/lib/ai/tools/get-vendor-spend-analysis';
import { getCategoryBreakdown } from '@/lib/ai/tools/get-category-breakdown';
import { forecastRevenue } from '@/lib/ai/tools/forecast-revenue';
import { computeCashFlow } from '@/lib/ai/tools/compute-cash-flow';
import { computeProfitAndLoss } from '@/lib/ai/tools/compute-profit-and-loss';
import { balanceSheetSnapshot } from '@/lib/ai/tools/balance-sheet-snapshot';
import { generateReport } from '@/lib/ai/tools/generate-report';
import { listReports } from '@/lib/ai/tools/list-reports';
import { downloadReport } from '@/lib/ai/tools/download-report';
import { scheduleReport } from '@/lib/ai/tools/schedule-report';
import { cancelScheduledReport } from '@/lib/ai/tools/cancel-scheduled-report';
import { sendReportEmail } from '@/lib/ai/tools/send-report-email';
import { emailReport } from '@/lib/ai/tools/email-report';
import db from '@/db/db';

export const maxDuration = 60;

export async function POST(request: Request) {
  const {
    id,
    messages,
    selectedChatModel,
  }: {
    id: string;
    messages: Array<Message>;
    selectedChatModel: string;
  } = await request.json();

  const userId = await getCurrentUserId();

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Check chat usage limits before processing
  const { checkCurrentUserChatUsage, incrementCurrentUserChatUsage } =
    await import('@/lib/services/usage-service');

  const usageCheck = await checkCurrentUserChatUsage();

  if (!usageCheck.allowed) {
    return new Response(usageCheck.message || 'Chat limit exceeded', {
      status: 429,
    });
  }

  const userMessage = getMostRecentUserMessage(messages);

  if (!userMessage) {
    return new Response('No user message found', { status: 400 });
  }

  // Check if chat exists
  const chat = await db.chat.findUnique({
    where: { id },
  });

  // If chat doesn't exist, create it
  if (!chat) {
    const title = await generateTitleFromUserMessage({
      message: userMessage,
    });
    await db.chat.create({
      data: {
        id,
        userId,
        title,
        createdAt: new Date(),
      },
    });
  }

  // Check if the message already exists (for edited messages)
  const existingMessage = await db.message.findUnique({
    where: { id: userMessage.id },
  });

  if (existingMessage) {
    // Update the existing message
    await db.message.update({
      where: { id: userMessage.id },
      data: {
        content: userMessage.content,
      },
    });
  } else {
    // Create a new message
    await db.message.create({
      data: {
        id: userMessage.id,
        chatId: id,
        role: userMessage.role,
        content: userMessage.content,
        createdAt: new Date(),
      },
    });
  }

  // --- OPTIMIZATION START ---
  // Use the fastest Groq model for best performance
  const modelToUse = 'billix-chat'; // Always use our optimized model
  // Minimal stream delay for instant response feel
  const streamDelay = 1; // ms - almost instant
  // Increased maxSteps for better tool usage
  const maxSteps = 8;
  // Longer timeout for complex operations
  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), 15000);
  
  // MESSAGE TRUNCATION - Keep only recent messages to avoid token limits
  const truncateMessages = (msgs: Array<Message>, maxMessages = 6) => {
    if (msgs.length <= maxMessages) return msgs;
    
    // Always keep the first message (usually system context) and recent messages
    const firstMessage = msgs[0];
    const recentMessages = msgs.slice(-maxMessages + 1);
    
    // If first message is user message, just return recent messages
    if (firstMessage.role === 'user') {
      return msgs.slice(-maxMessages);
    }
    
    return [firstMessage, ...recentMessages];
  };
  
  const truncatedMessages = truncateMessages(messages);
  
  // Enhanced logging for debugging
  console.log(`🚀 [BILLIX AI] Starting chat - Model: ${modelToUse}, User: ${userId}, Chat: ${id}`);
  console.log(`📝 [BILLIX AI] User message:`, JSON.stringify(userMessage.content).slice(0, 200));
  console.log(`⚙️ [BILLIX AI] Messages count: ${messages.length} -> ${truncatedMessages.length} (truncated), Stream delay: ${streamDelay}ms, Max steps: ${maxSteps}`);
  console.time(`billix-response-${id}`);
  // --- OPTIMIZATION END ---

  try {
    return createDataStreamResponse({
      execute: (dataStream) => {
        console.log(`🎯 [BILLIX AI] Executing streamText with ${modelToUse}`);
        
        const result = streamText({
          model: myProvider.languageModel(modelToUse),
          system: systemPrompt({ selectedChatModel: modelToUse }),
          messages,
          maxSteps,
          experimental_activeTools: [
            'getWeather',
            'createDocument',
            'updateDocument',
            'requestSuggestions',
            'listInvoices',
            'getInvoiceDetails',
            'createInvoice',
            'updateInvoiceStatus',
            'deleteInvoice',
            'categorizeInvoice',
            'createFolder',
            'listFolders',
            'addInvoiceToFolder',
            'removeInvoiceFromFolder',
            'getInvoiceStats',
            'getVendorSpendAnalysis',
            'getCategoryBreakdown',
            'forecastRevenue',
            'computeCashFlow',
            'computeProfitAndLoss',
            'balanceSheetSnapshot',
            'generateReport',
            'listReports',
            'downloadReport',
            'scheduleReport',
            'cancelScheduledReport',
            'sendReportEmail',
            'emailReport',
          ],
          experimental_transform: smoothStream({ chunking: 'word', delayInMs: streamDelay }),
          experimental_generateMessageId: generateUUID,
          onStepFinish: async ({ stepType, toolCalls, toolResults, text, finishReason }) => {
            console.log(`🔧 [BILLIX AI] Step finished - Type: ${stepType}, Tools called: ${toolCalls?.length || 0}`);
            if (toolCalls && toolCalls.length > 0) {
              toolCalls.forEach((call, index) => {
                console.log(`🛠️ [BILLIX AI] Tool ${index + 1}: ${call.toolName} with args:`, JSON.stringify(call.args).slice(0, 100));
              });
            }
            if (toolResults && toolResults.length > 0) {
              toolResults.forEach((result, index) => {
                console.log(`✅ [BILLIX AI] Tool result ${index + 1}:`, JSON.stringify(result.result).slice(0, 200));
              });
            }
            if (finishReason) {
              console.log(`🏁 [BILLIX AI] Step finish reason: ${finishReason}`);
            }
          },
          tools: {
            getWeather,
            createDocument: createDocument({ userId, dataStream }),
            updateDocument: updateDocument({ userId, dataStream }),
            requestSuggestions: requestSuggestions({
              userId,
              dataStream,
            }),
            listInvoices,
            getInvoiceDetails,
            createInvoice: createInvoice({
              userId: userId || '',
              dataStream,
            }),
            updateInvoiceStatus,
            deleteInvoice,
            categorizeInvoice,
            createFolder,
            listFolders,
            addInvoiceToFolder,
            removeInvoiceFromFolder,
            getInvoiceStats,
            getVendorSpendAnalysis,
            getCategoryBreakdown,
            forecastRevenue,
            computeCashFlow,
            computeProfitAndLoss,
            balanceSheetSnapshot,
            generateReport: generateReport({
              userId: userId || '',
              dataStream,
            }),
            listReports,
            downloadReport,
            scheduleReport,
            cancelScheduledReport,
            sendReportEmail: sendReportEmail({
              userId: userId || '',
              dataStream,
            }),
            emailReport: emailReport({
              userId: userId || '',
              dataStream,
            }),
          },
          onFinish: async ({ response, reasoning }) => {
            console.log(`✅ [BILLIX AI] Stream finished for chat ${id}`);
            console.timeEnd(`billix-response-${id}`);
            
            if (userId) {
              try {
                console.log(`💾 [BILLIX AI] Saving ${response.messages.length} response messages`);
                
                const sanitizedResponseMessages =
                  sanitizeResponseMessages({
                    messages: response.messages,
                    reasoning,
                  });

                console.log(`🧹 [BILLIX AI] Sanitized to ${sanitizedResponseMessages.length} messages`);

                // Save all response messages
                await db.message.createMany({
                  data: sanitizedResponseMessages.map((message) => {
                    return {
                      id: message.id,
                      chatId: id,
                      role: message.role,
                      content: JSON.stringify(message.content),
                      createdAt: new Date(),
                    };
                  }),
                });

                console.log(`✅ [BILLIX AI] Successfully saved messages to database`);

                // Increment chat usage counter after successful response
                await incrementCurrentUserChatUsage();
                console.log(`📊 [BILLIX AI] Incremented usage counter for user ${userId}`);
              } catch (error) {
                console.error(`❌ [BILLIX AI] Failed to save chat messages for chat ${id}:`, error);
                console.error(`🔍 [BILLIX AI] Error details:`, {
                  name: (error as Error)?.name,
                  message: (error as Error)?.message,
                  stack: (error as Error)?.stack?.split('\n').slice(0, 3).join('\n'),
                });
              }
            }
          },
          experimental_telemetry: {
            isEnabled: true,
            functionId: 'billix-ai-chat',
          },
          abortSignal: controller.signal,
        });

        result.consumeStream();

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: false, // No reasoning needed for fast responses
        });

        console.log(`🔄 [BILLIX AI] Starting stream consumption for chat ${id}`);
        result.consumeStream();

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: false, // No reasoning needed for fast responses
        });
      },
      onError: (error) => {
        console.error(`💥 [BILLIX AI] Stream error for chat ${id}:`, error);
        console.error(`🔍 [BILLIX AI] Stream error details:`, {
          name: (error as Error)?.name,
          message: (error as Error)?.message,
          stack: (error as Error)?.stack?.split('\n').slice(0, 5).join('\n'),
        });
        console.timeEnd(`billix-response-${id}`);
        return 'Oops, something went wrong! Please try again. I\'m here to help! 😊';
      },
    });
  } catch (error) {
    console.error(`🚨 [BILLIX AI] Critical error in chat ${id}:`, error);
    console.error(`🔍 [BILLIX AI] Critical error details:`, {
      name: (error as Error)?.name,
      message: (error as Error)?.message,
      stack: (error as Error)?.stack?.split('\n').slice(0, 5).join('\n'),
      userId,
      modelToUse,
      messageCount: messages.length,
    });
    console.timeEnd(`billix-response-${id}`);
    
    // Return a proper error response
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        message: 'Don\'t worry! Something went wrong on my end. Please try your question again! 🤖💪' 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  } finally {
    clearTimeout(timeout);
    console.log(`🏁 [BILLIX AI] Request completed for chat ${id}`);
  }
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('Not Found', { status: 404 });
  }

  const userId = await getCurrentUserId();

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const chat = await db.chat.findUnique({
      where: { id },
    });

    if (!chat) {
      return new Response('Chat not found', { status: 404 });
    }

    if (chat.userId !== userId) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Delete the chat (cascade will delete messages and votes)
    await db.chat.delete({
      where: { id },
    });

    return new Response('Chat deleted', { status: 200 });
  } catch (error) {
    console.error('Error deleting chat:', error);
    return new Response(
      'An error occurred while processing your request',
      {
        status: 500,
      }
    );
  }
}

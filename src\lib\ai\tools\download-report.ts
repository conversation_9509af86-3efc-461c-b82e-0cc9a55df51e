import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const downloadReport = tool({
  description: 'Provides the file URL or stream for a given report ID so the user can download it.',
  parameters: z.object({
    reportId: z.string().describe('The ID of the report to download'),
  }),
  execute: async ({ reportId }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();
      
      // Find the report in the database
      const report = await db.report.findUnique({
        where: { id: reportId },
      });
      
      if (!report) {
        return {
          success: false,
          message: `Report with ID ${reportId} not found. Please check the ID and try again.`,
        };
      }
      
      // Check if the report belongs to the current user
      if (report.userId !== userId) {
        return {
          success: false,
          message: 'You do not have permission to download this report.',
        };
      }
      
      // Log the download
      await db.reportDownload.create({
        data: {
          reportId,
          userId,
          downloadedAt: new Date(),
        },
      });
      
      // Return the download URL
      return {
        id: report.id,
        title: report.title,
        reportType: report.reportType,
        format: report.format,
        fileUrl: report.fileUrl,
        downloadUrl: `/api/reports/download/${reportId}`,
        message: `Report "${report.title}" is ready for download.`,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to download report. Please try again.',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

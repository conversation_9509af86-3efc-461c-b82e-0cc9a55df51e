import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';
import { DataStreamWriter } from 'ai';

interface SendReportEmailProps {
  userId: string;
  dataStream: DataStreamWriter;
}

export const sendReportEmail = ({ userId, dataStream }: SendReportEmailProps) => tool({
  description: 'Sends a report directly to the user\'s email address.',
  parameters: z.object({
    reportId: z.string().describe('The ID of the report to send'),
    emailAddresses: z.array(z.string().email()).min(1).describe('Email addresses to send the report to'),
    subject: z.string().optional().describe('Optional custom subject line for the email'),
    message: z.string().optional().describe('Optional custom message to include in the email body'),
  }),
  execute: async ({ reportId, emailAddresses, subject, message }) => {
    try {
      // Get the authenticated user ID
      const authenticatedUserId = userId || await getAuthenticatedUserId();

      // Find the report in the database - first try with exact ID match
      let report = await db.report.findUnique({
        where: { id: reportId },
      });

      // If not found by ID, try to find by title (for cases where the user refers to a report by name)
      if (!report) {
        // Get the most recent report that matches the title pattern
        report = await db.report.findFirst({
          where: {
            userId: authenticatedUserId,
            title: {
              contains: reportId,
              mode: 'insensitive'
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        });
      }

      if (!report) {
        return {
          success: false,
          message: `Report not found. Please check the report ID or name and try again.`,
        };
      }

      // Check if the report belongs to the current user
      if (report.userId !== authenticatedUserId) {
        return {
          success: false,
          message: 'You do not have permission to send this report.',
        };
      }

      // Signal the start of email sending to the UI
      dataStream.writeData({
        type: 'report-email-start',
        content: {
          id: reportId,
          title: report.title,
          emailAddresses,
        },
      });

      try {
        // Get the base URL for the API call
        const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_APP_URL || 'https://billix.io';

        // Call the API to send the email
        const response = await fetch(`${baseUrl}/api/reports/email/${report.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            emailAddresses,
            subject: subject || `Your Report: ${report.title}`,
            message: message || '',
          }),
        });

        if (!response.ok) {
          try {
            const errorData = await response.json();
            throw new Error(errorData.error || `Failed to send email: HTTP ${response.status}`);
          } catch {
            // If we can't parse the error as JSON, throw a more generic error
            throw new Error(`Failed to send email: HTTP ${response.status}`);
          }
        }

        const result = await response.json();

        // Signal completion
        dataStream.writeData({
          type: 'report-email-complete',
          content: {
            id: reportId,
            title: report.title,
            emailAddresses,
            sentAt: new Date().toISOString(),
            messageId: result.messageId,
          },
        });

        return {
          id: reportId,
          title: report.title,
          emailAddresses,
          message: `Report "${report.title}" sent successfully to ${emailAddresses.join(', ')}.`,
          messageId: result.messageId,
        };
      } catch (emailError) {
        // Signal error
        dataStream.writeData({
          type: 'report-email-error',
          content: {
            id: reportId,
            title: report.title,
            error: emailError instanceof Error ? emailError.message : String(emailError),
          },
        });

        return {
          success: false,
          message: 'Failed to send report email. Please try again.',
          error: emailError instanceof Error ? emailError.message : String(emailError),
        };
      }
    } catch (error) {
      // Signal error to the UI
      dataStream.writeData({
        type: 'report-email-error',
        content: {
          message: 'Failed to send report email. Please try again.',
          error: error instanceof Error ? error.message : String(error),
        },
      });

      return {
        success: false,
        message: 'Failed to send report email. Please try again.',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const forecastRevenue = tool({
  description: 'Projects future revenue (e.g., next month or quarter) based on historical invoice data and average issuance rates.',
  parameters: z.object({
    forecastPeriod: z.enum(['month', 'quarter', 'year']).describe('The period to forecast (month, quarter, or year)'),
    historicalMonths: z.number().min(1).max(36).default(6).optional().describe('Number of historical months to use for the forecast'),
    currency: z.string().optional().describe('Optional currency to filter by (e.g., USD, EUR)'),
    growthRate: z.number().optional().describe('Optional growth rate to apply to the forecast (e.g., 0.05 for 5% growth)'),
  }),
  execute: async ({ forecastPeriod, historicalMonths = 6, currency, growthRate }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Calculate the start date for historical data
      const now = new Date();
      const historicalStartDate = new Date(now);
      historicalStartDate.setMonth(now.getMonth() - historicalMonths);

      // Build the where clause for the query
      const where: Record<string, unknown> = {
        userId,
        createdAt: {
          gte: historicalStartDate,
        },
        status: {
          not: 'CANCELLED', // Exclude cancelled invoices
        },
      };

      // Add currency filter if provided
      if (currency) {
        where.currency = currency;
      }

      // Get all invoices for the user with the applied filters
      const invoices = await db.invoice.findMany({
        where,
        select: {
          id: true,
          amount: true,
          currency: true,
          issueDate: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      // Group invoices by month
      const monthlyRevenue: Record<string, {
        month: string;
        totalAmount: number;
        invoiceCount: number;
        currencies: Record<string, number>;
      }> = {};

      // Calculate monthly revenue
      for (const invoice of invoices) {
        if (!invoice.createdAt) continue;

        const monthYear = `${invoice.createdAt.getFullYear()}-${String(invoice.createdAt.getMonth() + 1).padStart(2, '0')}`;
        const amount = invoice.amount || 0;
        const currencyKey = invoice.currency || 'UNKNOWN';

        // Create month entry if it doesn't exist
        if (!monthlyRevenue[monthYear]) {
          monthlyRevenue[monthYear] = {
            month: monthYear,
            totalAmount: 0,
            invoiceCount: 0,
            currencies: {},
          };
        }

        // Update month stats
        monthlyRevenue[monthYear].totalAmount += amount;
        monthlyRevenue[monthYear].invoiceCount++;

        // Update currency breakdown
        if (!monthlyRevenue[monthYear].currencies[currencyKey]) {
          monthlyRevenue[monthYear].currencies[currencyKey] = 0;
        }
        monthlyRevenue[monthYear].currencies[currencyKey] += amount;
      }

      // Convert to array and sort by month
      const monthlyData = Object.values(monthlyRevenue)
        .sort((a, b) => a.month.localeCompare(b.month));

      // Calculate average monthly revenue and invoice count
      const totalMonths = monthlyData.length;
      const totalRevenue = monthlyData.reduce((sum, month) => sum + month.totalAmount, 0);
      const totalInvoiceCount = monthlyData.reduce((sum, month) => sum + month.invoiceCount, 0);

      const averageMonthlyRevenue = totalMonths > 0 ? totalRevenue / totalMonths : 0;
      const averageMonthlyInvoiceCount = totalMonths > 0 ? totalInvoiceCount / totalMonths : 0;

      // Calculate forecast based on the selected period
      let forecastAmount = 0;
      let forecastInvoiceCount = 0;
      let forecastPeriodMonths = 0;

      switch (forecastPeriod) {
        case 'month':
          forecastAmount = averageMonthlyRevenue;
          forecastInvoiceCount = averageMonthlyInvoiceCount;
          forecastPeriodMonths = 1;
          break;
        case 'quarter':
          forecastAmount = averageMonthlyRevenue * 3;
          forecastInvoiceCount = averageMonthlyInvoiceCount * 3;
          forecastPeriodMonths = 3;
          break;
        case 'year':
          forecastAmount = averageMonthlyRevenue * 12;
          forecastInvoiceCount = averageMonthlyInvoiceCount * 12;
          forecastPeriodMonths = 12;
          break;
      }

      // Apply growth rate if provided
      if (growthRate !== undefined) {
        forecastAmount *= (1 + growthRate);
        forecastInvoiceCount *= (1 + growthRate);
      }

      // Calculate forecast period dates
      const forecastStartDate = new Date(now);
      const forecastEndDate = new Date(now);
      forecastEndDate.setMonth(forecastEndDate.getMonth() + forecastPeriodMonths);

      // Calculate monthly breakdown for the forecast period
      const forecastMonthlyBreakdown = [];
      for (let i = 0; i < forecastPeriodMonths; i++) {
        const forecastMonth = new Date(now);
        forecastMonth.setMonth(forecastMonth.getMonth() + i);

        const monthYear = `${forecastMonth.getFullYear()}-${String(forecastMonth.getMonth() + 1).padStart(2, '0')}`;
        const monthlyAmount = averageMonthlyRevenue * (growthRate !== undefined ? (1 + growthRate) : 1);
        const monthlyInvoiceCount = averageMonthlyInvoiceCount * (growthRate !== undefined ? (1 + growthRate) : 1);

        forecastMonthlyBreakdown.push({
          month: monthYear,
          forecastAmount: monthlyAmount,
          forecastInvoiceCount: monthlyInvoiceCount,
        });
      }

      // Prepare the result
      return {
        historicalData: {
          months: monthlyData,
          averageMonthlyRevenue,
          averageMonthlyInvoiceCount,
          totalRevenue,
          totalInvoiceCount,
          periodStart: historicalStartDate.toISOString(),
          periodEnd: now.toISOString(),
        },
        forecast: {
          period: forecastPeriod,
          forecastAmount,
          forecastInvoiceCount,
          growthRateApplied: growthRate !== undefined ? growthRate : 0,
          periodStart: forecastStartDate.toISOString(),
          periodEnd: forecastEndDate.toISOString(),
          monthlyBreakdown: forecastMonthlyBreakdown,
        },
        systemDate: {
          current: now.toISOString(),
          currentFormatted: now.toISOString().split('T')[0],
          year: now.getFullYear(),
          month: now.getMonth() + 1, // JavaScript months are 0-indexed
          day: now.getDate()
        },
        message: "Note: This revenue forecast is based on invoice creation dates (when they were added to the system), not the issue dates shown on the invoices."
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to forecast revenue. Please try again.',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

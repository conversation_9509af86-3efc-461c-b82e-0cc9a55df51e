import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const createFolder = tool({
  description: 'Creates a new folder for grouping or exporting invoices under the user\'s account.',
  parameters: z.object({
    folderName: z.string().describe('The name of the folder to create'),
    description: z.string().optional().describe('Optional description for the folder'),
  }),
  execute: async ({ folderName }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Check if a folder with the same name already exists for this user
      const existingFolder = await db.exportFolder.findFirst({
        where: {
          name: {
            equals: folderName,
            mode: 'insensitive',
          },
          userId,
        },
      });

      if (existingFolder) {
        return {
          success: false,
          message: `A folder named "${folderName}" already exists. Please choose a different name.`,
        };
      }

      // Create a new folder
      const newFolder = await db.exportFolder.create({
        data: {
          name: folderName,
          userId,
          invoiceCount: 0,
        },
      });

      return {
        id: newFolder.id,
        name: newFolder.name,
        message: `Folder "${folderName}" created successfully.`,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to create folder. Please try again.',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});

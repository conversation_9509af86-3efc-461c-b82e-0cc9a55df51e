import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const computeProfitAndLoss = tool({
  description: 'Generates a P&L summary (total revenue minus total costs) for a specified period, using invoice amounts and any cost data.',
  parameters: z.object({
    startDate: z.string().describe('Start date for the period to analyze (YYYY-MM-DD)'),
    endDate: z.string().describe('End date for the period to analyze (YYYY-MM-DD)'),
    currency: z.string().optional().describe('Optional currency to filter by (e.g., USD, EUR)'),
    costEstimationMethod: z.enum(['percentage', 'category', 'none']).default('percentage').optional()
      .describe('Method to estimate costs: percentage (of revenue), category (based on invoice categories), or none'),
    costPercentage: z.number().min(0).max(100).default(70).optional()
      .describe('Estimated cost percentage of revenue (0-100), used when costEstimationMethod is "percentage"'),
    categoryCosts: z.array(
      z.object({
        categoryName: z.string().describe('Name of the category'),
        costPercentage: z.number().min(0).max(100).describe('Cost percentage for this category (0-100)'),
      })
    ).optional().describe('Category-specific cost percentages, used when costEstimationMethod is "category"'),
  }),
  execute: async ({ 
    startDate, 
    endDate, 
    currency, 
    costEstimationMethod = 'percentage', 
    costPercentage = 70, 
    categoryCosts = [] 
  }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Build the where clause for the query
      const where: Record<string, unknown> = {
        userId,
        issueDate: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
        status: 'PAID', // Only include paid invoices in P&L
      };

      // Add currency filter if provided
      if (currency) {
        where.currency = currency;
      }

      // Get all invoices for the user with the applied filters
      const invoices = await db.invoice.findMany({
        where,
        select: {
          id: true,
          amount: true,
          currency: true,
          issueDate: true,
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          issueDate: 'asc',
        },
      });

      // Convert category costs array to a map for easier lookup
      const categoryCostsMap: Record<string, number> = {};
      categoryCosts.forEach(item => {
        categoryCostsMap[item.categoryName.toLowerCase()] = item.costPercentage;
      });

      // Group invoices by category for revenue and cost calculation
      const categoryBreakdown: Record<string, {
        categoryName: string;
        revenue: number;
        costs: number;
        profit: number;
        margin: number;
        invoiceCount: number;
      }> = {};

      // Calculate revenue and costs by category
      let totalRevenue = 0;
      let totalCosts = 0;

      for (const invoice of invoices) {
        const amount = invoice.amount || 0;
        const categoryName = invoice.category?.name || 'Uncategorized';
        const categoryKey = categoryName.toLowerCase();
        
        // Create category entry if it doesn't exist
        if (!categoryBreakdown[categoryKey]) {
          categoryBreakdown[categoryKey] = {
            categoryName,
            revenue: 0,
            costs: 0,
            profit: 0,
            margin: 0,
            invoiceCount: 0,
          };
        }

        // Update category revenue
        categoryBreakdown[categoryKey].revenue += amount;
        categoryBreakdown[categoryKey].invoiceCount++;
        totalRevenue += amount;

        // Calculate costs based on the selected method
        let costAmount = 0;
        if (costEstimationMethod === 'percentage') {
          costAmount = amount * (costPercentage / 100);
        } else if (costEstimationMethod === 'category') {
          const categoryCostPercentage = categoryCostsMap[categoryKey] !== undefined 
            ? categoryCostsMap[categoryKey] 
            : costPercentage; // Fall back to default if category not found
          costAmount = amount * (categoryCostPercentage / 100);
        }

        if (costEstimationMethod !== 'none') {
          categoryBreakdown[categoryKey].costs += costAmount;
          totalCosts += costAmount;
        }
      }

      // Calculate profit and margin for each category
      for (const category in categoryBreakdown) {
        const data = categoryBreakdown[category];
        data.profit = data.revenue - data.costs;
        data.margin = data.revenue > 0 ? (data.profit / data.revenue) * 100 : 0;
      }

      // Convert to array and sort by revenue
      const categoriesData = Object.values(categoryBreakdown)
        .sort((a, b) => b.revenue - a.revenue);

      // Calculate overall profit and margin
      const totalProfit = totalRevenue - totalCosts;
      const overallMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

      // Prepare the result
      return {
        profitAndLoss: {
          totalRevenue,
          totalCosts,
          totalProfit,
          overallMargin,
          invoiceCount: invoices.length,
          categoryBreakdown: categoriesData,
          costEstimationMethod: costEstimationMethod === 'none' 
            ? 'No cost estimation' 
            : costEstimationMethod === 'percentage' 
              ? `${costPercentage}% of revenue` 
              : 'Category-specific percentages',
        },
        timeframe: {
          startDate: new Date(startDate).toISOString(),
          endDate: new Date(endDate).toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to compute profit and loss. Please try again.',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

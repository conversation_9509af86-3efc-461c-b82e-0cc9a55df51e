import { groq } from '@ai-sdk/groq';
import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from 'ai';

export const DEFAULT_CHAT_MODEL: string = 'billix-chat';

export const myProvider = customProvider({
  languageModels: {
    'billix-chat': groq('meta-llama/llama-4-maverick-17b-128e-instruct'), // Fast, high-quality Groq model
    'title-model': groq('deepseek-r1-distill-llama-70b'), // Faster model for titles
    'artifact-model': groq('deepseek-r1-distill-llama-70b'),
    'invoice-extraction-model': groq('deepseek-r1-distill-llama-70b'),
  },
});

interface ChatModel {
  id: string;
  name: string;
  description: string;
}

export const chatModels: Array<ChatModel> = [
  {
    id: 'billix-chat',
    name: 'Billix <PERSON>',
    description: 'Advanced AI assistant for financial management',
  },
];

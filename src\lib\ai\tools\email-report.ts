import { DataStreamWriter, tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

interface EmailReportProps {
  userId: string;
  dataStream: DataStreamWriter;
}

export const emailReport = ({ userId, dataStream }: EmailReportProps) =>
  tool({
    description: 'Sends a previously generated report to specified email addresses.',
    parameters: z.object({
      reportId: z.string().describe('The ID of the report to email'),
      emailAddresses: z.array(z.string().email()).min(1).describe('Email addresses to send the report to'),
      subject: z.string().optional().describe('Optional custom subject line for the email'),
      message: z.string().optional().describe('Optional custom message to include in the email body'),
      includeExcel: z.boolean().default(false).optional().describe('Whether to include an Excel version of the report'),
    }),
    execute: async ({ reportId, emailAddresses, subject, message, includeExcel = false }) => {
      try {
        // Signal the start of email sending
        dataStream.writeData({
          type: 'report-email-start',
          content: {
            reportId,
            emailAddresses,
          },
        });

        // Get the authenticated user ID
        const authenticatedUserId = userId || await getAuthenticatedUserId();

        // Find the report - first try with exact ID match
        let report = await db.report.findFirst({
          where: {
            id: reportId,
            userId: authenticatedUserId,
          },
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });

        // If not found by ID, try to find by title (for cases where the user refers to a report by name)
        if (!report) {
          // Get the most recent report that matches the title pattern
          report = await db.report.findFirst({
            where: {
              userId: authenticatedUserId,
              title: {
                contains: reportId,
                mode: 'insensitive'
              }
            },
            orderBy: {
              createdAt: 'desc'
            },
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          });
        }

        if (!report) {
          const errorMessage = `Report not found. Please check the report ID or name and try again.`;

          dataStream.writeData({
            type: 'report-email-error',
            content: {
              reportId,
              error: errorMessage,
            },
          });

          throw new Error(errorMessage);
        }

        // We now have a valid report object with the correct ID

        if (!report.fileUrl) {
          dataStream.writeData({
            type: 'report-email-error',
            content: {
              reportId,
              error: 'Report file is not available for sending',
            },
          });

          throw new Error('Report file is not available for sending');
        }

        // Include Excel version in the API request if requested
        // The API will handle generating the Excel version if needed

        // Get the base URL for the API call
        const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_APP_URL || 'https://billix.io';

        // Use the API endpoint to send the email instead of direct Resend integration
        const response = await fetch(`${baseUrl}/api/reports/email/${report.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            emailAddresses,
            subject: subject || `Your Report: ${report.title}`,
            message: message || '',
            includeExcel,
          }),
        });

        if (!response.ok) {
          try {
            const errorData = await response.json();
            throw new Error(errorData.error || `Failed to send email: HTTP ${response.status}`);
          } catch {
            // If we can't parse the error as JSON, throw a more generic error
            throw new Error(`Failed to send email: HTTP ${response.status}`);
          }
        }

        const result = await response.json();
        const error = result.error;

        if (error) {
          // Signal error if not already done
          dataStream.writeData({
            type: 'report-email-error',
            content: {
              reportId,
              error: `Failed to send email: ${error.message}`,
            },
          });

          throw new Error(`Failed to send email: ${error.message}`);
        }

        // Log the email in the database
        await db.reportEmail.create({
          data: {
            reportId: report.id,
            userId: authenticatedUserId,
            emailAddresses: emailAddresses.join(','),
            subject: subject || `Your Report: ${report.title} is Ready`,
            message: message || '',
            sentAt: new Date(),
          },
        });

        // Signal completion
        dataStream.writeData({
          type: 'report-email-complete',
          content: {
            reportId,
            emailAddresses,
            sentAt: new Date().toISOString(),
          },
        });

        dataStream.writeData({ type: 'finish', content: '' });

        return {
          success: true,
          reportId: report.id,
          title: report.title,
          emailAddresses,
          message: `Report "${report.title}" has been sent to ${emailAddresses.join(', ')}.`,
        };
      } catch (error) {
        // Provide more context if it's a URL error
        const errorMessage = error instanceof Error
          ? (error.message.includes('URL')
              ? `URL error: Make sure the API URL is correct. ${error.message}`
              : error.message)
          : String(error);

        // Signal error if not already done
        dataStream.writeData({
          type: 'report-email-error',
          content: {
            reportId,
            error: errorMessage,
          },
        });

        dataStream.writeData({ type: 'finish', content: '' });

        throw new Error(`Failed to email report: ${errorMessage}`);
      }
    },
  });

import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';
import { Prisma } from '@prisma/client';

export const listInvoices = tool({
  description: 'Retrieves a list of the authenticated user\'s invoices, optionally filtered by status, date range, vendor, category, or search term. IMPORTANT: When filtering by date, use the invoice creation date (when it was added to the system), not the issue date shown on the invoice.',
  parameters: z.object({
    status: z.enum(['PENDING', 'PAID', 'OVERDUE', 'CANCELLED']).optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    vendor: z.string().optional(),
    category: z.string().optional(),
    searchTerm: z.string().optional(),
    limit: z.number().min(1).max(100).default(20).optional(),
    page: z.number().min(1).default(1).optional(),
  }),
  execute: async ({ status, startDate, endDate, vendor, category, searchTerm, limit = 20, page = 1 }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Build the where clause for the query
      const where: Prisma.InvoiceWhereInput = {
        // Always filter by the current user
        userId,
      };

      // Add filters based on parameters
      if (status) {
        where.status = status;
      }

      if (startDate || endDate) {
        where.createdAt = {};

        if (startDate) {
          where.createdAt.gte = new Date(startDate);
        }

        if (endDate) {
          where.createdAt.lte = new Date(endDate);
        }
      }

      if (vendor) {
        where.vendorName = {
          contains: vendor,
          mode: 'insensitive',
        };
      }

      if (category) {
        where.category = {
          name: {
            contains: category,
            mode: 'insensitive',
          },
        };
      }

      if (searchTerm) {
        where.OR = [
          {
            invoiceNumber: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
          {
            vendorName: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
          {
            notes: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
        ];
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Query the database
      const [invoices, total] = await Promise.all([
        db.invoice.findMany({
          where,
          include: {
            lineItems: true,
            category: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
          skip,
          take: limit,
        }),
        db.invoice.count({ where }),
      ]);

      // Format the response
      return {
        invoices: invoices.map(invoice => ({
          id: invoice.id,
          number: invoice.invoiceNumber || '',
          vendorName: invoice.vendorName || '',
          amount: invoice.amount || 0,
          currency: invoice.currency || 'USD',
          status: invoice.status,
          category: invoice.category?.name,
          issueDate: invoice.issueDate ? invoice.issueDate.toISOString().split('T')[0] : null,
          dueDate: invoice.dueDate ? invoice.dueDate.toISOString().split('T')[0] : null,
          createdAt: invoice.createdAt ? invoice.createdAt.toISOString().split('T')[0] : null,
          updatedAt: invoice.updatedAt ? invoice.updatedAt.toISOString().split('T')[0] : null,
          lineItems: invoice.lineItems.map(item => ({
            id: item.id,
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            amount: item.totalPrice,
          })),
        })),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        systemDate: {
          current: new Date().toISOString(),
          currentFormatted: new Date().toISOString().split('T')[0],
          year: new Date().getFullYear(),
          month: new Date().getMonth() + 1, // JavaScript months are 0-indexed
          day: new Date().getDate()
        }, // Include detailed current date information
        message: "Note: When filtering by date, the system uses the invoice creation date (when it was added to the system), not the issue date shown on the invoice."
      };
    } catch {
      throw new Error('Failed to retrieve invoices. Please try again.');
    }
  },
});

import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const removeInvoiceFromFolder = tool({
  description: 'Unlinks an invoice from a specific folder without deleting the folder or invoice.',
  parameters: z.object({
    invoiceId: z.string().describe('The ID of the invoice to remove from the folder'),
    folderId: z.string().describe('The ID of the folder to remove the invoice from'),
  }),
  execute: async ({ invoiceId, folderId }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Check if the invoice exists and belongs to the current user
      let invoice = await db.invoice.findUnique({
        where: { id: invoiceId },
      });

      // If not found by ID, try to find by invoice number
      if (!invoice) {
        invoice = await db.invoice.findFirst({
          where: {
            invoiceNumber: invoiceId,
            userId
          },
        });
      }

      if (!invoice) {
        return {
          success: false,
          message: `Invoice with ID or number ${invoiceId} not found. Please check the ID and try again.`,
        };
      }

      // Check if the invoice belongs to the current user
      if (invoice.userId !== userId) {
        return {
          success: false,
          message: 'You do not have permission to access this invoice.',
        };
      }

      // Check if the folder exists and belongs to the current user
      const folder = await db.exportFolder.findFirst({
        where: {
          id: folderId,
          userId,
        },
      });

      if (!folder) {
        return {
          success: false,
          message: `Folder with ID ${folderId} not found. Please check the folder ID and try again.`,
        };
      }

      // Check if the invoice is in the folder
      const folderInvoice = await db.folderInvoice.findUnique({
        where: {
          folderId_invoiceId: {
            folderId,
            invoiceId,
          },
        },
      });

      if (!folderInvoice) {
        return {
          invoiceId,
          folderId,
          folderName: folder.name,
          message: `Invoice ${invoice.invoiceNumber || ''} is not in folder "${folder.name}".`,
        };
      }

      // Remove the invoice from the folder
      await db.$transaction(async (prisma) => {
        // Delete the relation
        await prisma.folderInvoice.delete({
          where: {
            folderId_invoiceId: {
              folderId,
              invoiceId,
            },
          },
        });

        // Update the folder's invoice count
        await prisma.exportFolder.update({
          where: { id: folderId },
          data: {
            invoiceCount: {
              decrement: 1,
            },
            updatedAt: new Date(),
          },
        });
      });

      return {
        invoiceId,
        invoiceNumber: invoice.invoiceNumber || '',
        folderId,
        folderName: folder.name,
        message: `Invoice ${invoice.invoiceNumber || ''} removed from folder "${folder.name}".`,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to remove invoice from folder. Please try again.',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});

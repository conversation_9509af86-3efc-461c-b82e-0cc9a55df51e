import {
  type Message,
  createDataStreamResponse,
  streamText,
} from 'ai';
import { myProvider, selectOptimalModel } from '@/lib/ai/models';
import { ContextEngine, generateContextualPrompt } from '@/lib/ai/context-engine';
import { ActionEngine, detectIntent } from '@/lib/ai/action-engine';
import { MemoryEngine } from '@/lib/ai/memory-engine';
import { PredictionEngine } from '@/lib/ai/prediction-engine';
import { AnalyticsEngine } from '@/lib/ai/analytics-engine';
import { PerformanceMonitor } from '@/lib/ai/performance-monitor';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import { generateUUID } from '@/lib/utils';
import db from '@/db/db';

export async function POST(request: Request) {
  const {
    id,
    messages,
  }: {
    id: string;
    messages: Array<Message>;
  } = await request.json();

  const userId = await getCurrentUserId();

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Check usage limits
  const { checkCurrentUserChatUsage, incrementCurrentUserChatUsage } =
    await import('@/lib/services/usage-service');

  const usageCheck = await checkCurrentUserChatUsage();
  if (!usageCheck.allowed) {
    return new Response(usageCheck.message || 'Chat limit exceeded', {
      status: 429,
    });
  }

  const userMessage = messages[messages.length - 1];
  if (!userMessage || userMessage.role !== 'user') {
    return new Response('Invalid message format', { status: 400 });
  }

  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), 6000); // 6 second timeout
  const startTime = Date.now();

  try {
    // SMART OPTIMIZATION: Get comprehensive user intelligence
    const [userContext, conversationMemory, userProfile, predictiveInsights, analytics, optimalModel] = await Promise.all([
      ContextEngine.getUserContext(userId),
      MemoryEngine.getConversationMemory(id, userId),
      MemoryEngine.getUserProfile(userId),
      PredictionEngine.generateInsights(userId),
      AnalyticsEngine.generateAdvancedAnalytics(userId),
      Promise.resolve(selectOptimalModel(userMessage.content as string))
    ]);

    // Generate enhanced contextual system prompt with full intelligence
    let systemPrompt = generateContextualPrompt(userContext);

    if (conversationMemory) {
      systemPrompt += `\n\n${MemoryEngine.generateConversationSummary(conversationMemory, userProfile)}`;
    }

    // Add proactive insights if relevant
    if (predictiveInsights.length > 0) {
      const topInsight = predictiveInsights[0];
      systemPrompt += `\n\nPROACTIVE INSIGHT: ${topInsight.title} - ${topInsight.description}`;
    }

    // Add business intelligence context
    systemPrompt += `\n\nBUSINESS HEALTH: ${analytics.financialHealth.status} (${analytics.financialHealth.score}/100)`;
    if (analytics.recommendations.length > 0) {
      systemPrompt += `\nTOP RECOMMENDATION: ${analytics.recommendations[0].title}`;
    }

    // Aggressive message truncation - keep only last 2 messages + system context
    const truncatedMessages = messages.slice(-2);

    // Detect if user wants to perform an action
    const intentDetection = detectIntent(userMessage.content as string);

    return createDataStreamResponse({
      execute: async (dataStream) => {
        // If high-confidence action detected, execute it
        if (intentDetection.confidence > 0.7) {
          try {
            const actionResult = await ActionEngine.executeAction(
              intentDetection.intent,
              intentDetection.parameters,
              userId
            );

            if (actionResult.success) {
              // Stream the action result as part of the AI response
              dataStream.writeData({
                type: 'action-result',
                result: {
                  success: actionResult.success,
                  message: actionResult.message,
                  type: actionResult.type,
                  data: actionResult.data ? JSON.stringify(actionResult.data) : null
                }
              });
            }
          } catch (error) {
            console.error('Action execution error:', error);
          }
        }

        // Generate AI response with optimal model selection
        console.log(`🧠 [SMART AI] Using model: ${optimalModel} for query type`);

        const result = streamText({
          model: myProvider.languageModel(optimalModel),
          system: systemPrompt,
          messages: truncatedMessages,
          maxSteps: 1, // Single step for speed
          experimental_generateMessageId: generateUUID,
          abortSignal: controller.signal,
          experimental_telemetry: {
            isEnabled: true,
            functionId: `billix-smart-${optimalModel}`,
          },
        });

        // Stream the response
        result.mergeIntoDataStream(dataStream);

        // Handle completion in background (fire and forget)
        setTimeout(async () => {
          try {
            // Update conversation memory with new message
            await MemoryEngine.updateConversationMemory(id, userId, userMessage);

            // Save the user message
            await db.message.create({
              data: {
                id: generateUUID(),
                chatId: id,
                role: 'user',
                content: JSON.stringify(userMessage.content),
                createdAt: new Date(),
              }
            });

            await incrementCurrentUserChatUsage();

            // Clear context cache to ensure fresh data on next request
            ContextEngine.clearCache(userId);

            // Record performance metrics
            PerformanceMonitor.recordMetrics({
              responseTime: Date.now() - startTime,
              tokenUsage: (userMessage.content as string).length + 500, // Estimate
              cacheHitRate: 0.8, // Estimate based on cache usage
              modelUsed: optimalModel,
              success: true,
              userId,
              queryType: detectIntent(userMessage.content as string).intent
            });
          } catch (error) {
            console.error('Failed to save messages:', error);
          }
        }, 100);
      },
      onError: (error) => {
        console.error('Smart chat error:', error);
        return 'I apologize, but I encountered an issue. Please try again.';
      },
    });
  } catch (error) {
    console.error('Critical smart chat error:', error);
    
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: 'Something went wrong. Please try again!'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  } finally {
    clearTimeout(timeout);
  }
}



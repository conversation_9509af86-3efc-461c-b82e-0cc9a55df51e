import {
  type Message,
  createDataStreamResponse,
  streamText,
} from 'ai';
import { myProvider, selectOptimalModel } from '@/lib/ai/models';
import { ContextEngine, generateContextualPrompt } from '@/lib/ai/context-engine';
import { ActionEngine, detectIntent } from '@/lib/ai/action-engine';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import { generateUUID, sanitizeResponseMessages } from '@/lib/utils';
import db from '@/db/db';

export async function POST(request: Request) {
  const {
    id,
    messages,
  }: {
    id: string;
    messages: Array<Message>;
  } = await request.json();

  const userId = await getCurrentUserId();

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Check usage limits
  const { checkCurrentUserChatUsage, incrementCurrentUserChatUsage } =
    await import('@/lib/services/usage-service');

  const usageCheck = await checkCurrentUserChatUsage();
  if (!usageCheck.allowed) {
    return new Response(usageCheck.message || 'Chat limit exceeded', {
      status: 429,
    });
  }

  const userMessage = messages[messages.length - 1];
  if (!userMessage || userMessage.role !== 'user') {
    return new Response('Invalid message format', { status: 400 });
  }

  try {
    // SMART OPTIMIZATION: Get user context and select optimal model
    const [userContext, optimalModel] = await Promise.all([
      ContextEngine.getUserContext(userId),
      Promise.resolve(selectOptimalModel(userMessage.content as string))
    ]);

    // Generate contextual system prompt
    const systemPrompt = generateContextualPrompt(userContext);

    // Aggressive message truncation - keep only last 2 messages + system context
    const truncatedMessages = messages.slice(-2);

    // Detect if user wants to perform an action
    const intentDetection = detectIntent(userMessage.content as string);
    
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 6000); // 6 second timeout

    return createDataStreamResponse({
      execute: async (dataStream) => {
        // If high-confidence action detected, execute it
        if (intentDetection.confidence > 0.7) {
          try {
            const actionResult = await ActionEngine.executeAction(
              intentDetection.intent,
              intentDetection.parameters,
              userId
            );

            if (actionResult.success) {
              // Stream the action result as part of the AI response
              dataStream.writeData({
                type: 'action-result',
                result: actionResult
              });
            }
          } catch (error) {
            console.error('Action execution error:', error);
          }
        }

        // Generate AI response with context
        const result = streamText({
          model: myProvider.languageModel(optimalModel),
          system: systemPrompt,
          messages: truncatedMessages,
          maxSteps: 1, // Single step for speed
          experimental_generateMessageId: generateUUID,
          abortSignal: controller.signal,
          experimental_telemetry: {
            isEnabled: true,
            functionId: 'billix-smart-chat',
          },
        });

        // Stream the response
        result.mergeIntoDataStream(dataStream);

        // Save messages after streaming
        result.onFinish(async ({ response, reasoning }) => {
          if (userId) {
            try {
              const sanitizedResponseMessages = sanitizeResponseMessages({
                messages: response.messages,
                reasoning,
              });

              await db.message.createMany({
                data: sanitizedResponseMessages.map((message) => ({
                  id: message.id,
                  chatId: id,
                  role: message.role,
                  content: JSON.stringify(message.content),
                  createdAt: new Date(),
                })),
              });

              await incrementCurrentUserChatUsage();
              
              // Clear context cache to ensure fresh data on next request
              ContextEngine.clearCache(userId);
            } catch (error) {
              console.error('Failed to save messages:', error);
            }
          }
        });
      },
      onError: (error) => {
        console.error('Smart chat error:', error);
        return 'I apologize, but I encountered an issue. Please try again.';
      },
    });
  } catch (error) {
    console.error('Critical smart chat error:', error);
    
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: 'Something went wrong. Please try again!'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  } finally {
    clearTimeout(timeout);
  }
}

/**
 * Enhanced message processing with intelligent context injection
 */
function enhanceMessagesWithContext(
  messages: Array<Message>, 
  context: any
): Array<Message> {
  // Add context hints to the last user message
  const lastMessage = messages[messages.length - 1];
  if (lastMessage && lastMessage.role === 'user') {
    const enhancedContent = `${lastMessage.content}

[Context: User has ${context.business.totalInvoices} invoices, $${context.business.pendingAmount} pending]`;
    
    return [
      ...messages.slice(0, -1),
      {
        ...lastMessage,
        content: enhancedContent
      }
    ];
  }
  
  return messages;
}

/**
 * Smart response enhancement based on action results
 */
function enhanceResponseWithActions(
  response: string, 
  actionResults: any[]
): string {
  if (actionResults.length === 0) return response;
  
  let enhanced = response;
  
  for (const result of actionResults) {
    if (result.success) {
      enhanced += `\n\n✅ ${result.message}`;
      
      // Add specific data formatting based on action type
      if (result.type === 'invoice' && result.data) {
        enhanced += `\n📄 Invoice: ${result.data.invoiceNumber} - $${result.data.amount}`;
      } else if (result.type === 'analytics' && result.data) {
        enhanced += `\n📊 Stats: ${JSON.stringify(result.data, null, 2)}`;
      }
    } else {
      enhanced += `\n\n❌ ${result.message}`;
    }
  }
  
  return enhanced;
}

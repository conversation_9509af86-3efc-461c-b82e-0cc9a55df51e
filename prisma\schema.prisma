generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                 String                 @id @default(cuid())
  clerkId            String                 @unique
  email              String                 @unique
  firstName          String?
  lastName           String?
  profileImageUrl    String?
  createdAt          DateTime               @default(now())
  updatedAt          DateTime               @updatedAt
  role               UserRole               @default(USER)
  lastActive         DateTime?
  status             UserStatus             @default(ACTIVE)
  aiSettings         AISettings?
  categories         Category[]
  chats              Chat[]
  customDataMappings CustomDataMapping[]
  documents          Document[]
  emailIntegrations  EmailIntegration[]
  emailSyncHistory   EmailSyncHistory[]
  emailSyncJobs      EmailSyncJob[]
  exportFolders      ExportFolder[]
  exports            ExportHistory[]
  invoices           Invoice[]
  invoiceHistory     InvoiceHistory[]
  reports            Report[]
  reportDownloads    ReportDownload[]
  reportEmails       ReportEmail[]
  reportTemplates    ReportTemplate[]
  scheduledReports   ScheduledReport[]
  suggestions        Suggestion[]
  sentInvites        TeamInvite[]           @relation("InvitedByUser")
  vendors            Vendor[]
  vendorProfiles     VendorProfile[]
  vendorPreferences  VendorTypePreference[]
  Subscription       Subscription[]
  organizations         Organization[]         @relation("OrganizationToUser")
  userUsage             UserUsage?
  invoiceEnhancementJobs InvoiceEnhancementJob[]
  apiKeys               UsersApiKey[]
  apiUsage              ApiUsage?
}

model AISettings {
  id                       String   @id @default(cuid())
  userId                   String   @unique
  customInstructions       String?
  confidenceThreshold      Float    @default(0.7)
  preferredCategories      String[]
  sampleInvoiceUrls        String[]
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt
  commonFormats            String[] @default([])
  customRules              Json?
  documentTypes            String[] @default([])
  enableContinuousLearning Boolean  @default(true)
  extractionPreferences    Json?
  organizationVocabulary   String[] @default([])
  vendorProfiles           Json?
  fraudDetectionRules      Json?
  learningFeedback         Json?
  historicalPatterns       Json?
  user                     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model CustomDataMapping {
  id                 String   @id @default(cuid())
  userId             String
  sourceField        String
  targetField        String
  transformationRule String?
  priority           Int      @default(0)
  applies            Json?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  user               User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, sourceField, targetField])
  @@index([userId])
}

model InvoiceLineItem {
  id          String   @id @default(cuid())
  invoiceId   String
  description String
  quantity    Float
  unitPrice   Float
  totalPrice  Float
  taxRate     Float?
  taxAmount   Float?
  discount    Float?
  productSku  String?
  notes       String?
  attributes  Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  invoice     Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@index([invoiceId])
}

model Organization {
  id               String            @id @default(cuid())
  name             String
  logoUrl          String?
  industry         String?
  size             String?
  invoiceVolume    String?
  clerkOrgId       String?           @unique
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  categories       Category[]
  exports          ExportHistory[]
  invoices         Invoice[]
  reports          Report[]
  reportTemplates  ReportTemplate[]
  scheduledReports ScheduledReport[]
  teamInvites      TeamInvite[]
  vendors          Vendor[]
  members          User[]            @relation("OrganizationToUser")
}

model Invoice {
  id                   String            @id @default(cuid())
  invoiceNumber        String?
  title                String?
  vendorName           String?
  issueDate            DateTime?
  dueDate              DateTime?
  paidDate             DateTime?
  amount               Float?
  currency             String?           @default("USD")
  status               InvoiceStatus     @default(PENDING)
  invoiceType          String?           @default("PURCHASE")
  notes                String?
  tags                 String[]
  categoryId           String?
  vendorId             String?
  originalFileUrl      String?
  thumbnailUrl         String?
  extractedData        Json?
  languageCode         String?
  createdAt            DateTime          @default(now())
  updatedAt            DateTime          @updatedAt
  userId               String
  organizationId       String?
  extractionConfidence Float?
  auditStatus          String?
  fraudScore           Float?
  relatedDocuments     Json?
  paymentPrediction    Json?
  exchangeRates        Json?
  folders              FolderInvoice[]
  category             Category?         @relation(fields: [categoryId], references: [id])
  organization         Organization?     @relation(fields: [organizationId], references: [id])
  user                 User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  vendor               Vendor?           @relation(fields: [vendorId], references: [id])
  audit                InvoiceAudit[]
  history              InvoiceHistory[]
  lineItems            InvoiceLineItem[]
  reportData           ReportData[]
  enhancementJobs      InvoiceEnhancementJob[]
}

model Category {
  id             String        @id @default(cuid())
  name           String
  description    String?
  color          String?
  icon           String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  userId         String
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  user           User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  invoices       Invoice[]

  @@unique([name, userId])
}

model Vendor {
  id             String          @id @default(cuid())
  name           String
  email          String?
  phone          String?
  website        String?
  address        String?
  notes          String?
  logoUrl        String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  userId         String
  organizationId String?
  invoices       Invoice[]
  organization   Organization?   @relation(fields: [organizationId], references: [id])
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  profiles       VendorProfile[]

  @@unique([name, userId])
}

model VendorProfile {
  id                 String   @id @default(cuid())
  vendorId           String
  userId             String
  documentFormat     String?
  extractionPatterns Json?
  fieldMappings      Json?
  commonLineItems    Json?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  user               User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  vendor             Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  @@index([vendorId])
  @@index([userId])
}

model InvoiceAudit {
  id            String   @id @default(cuid())
  invoiceId     String
  status        String
  fraudScore    Float?
  issues        Json?
  taxCompliance Json?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  invoice       Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@index([invoiceId])
}

model InvoiceHistory {
  id        String   @id @default(cuid())
  invoiceId String
  userId    String
  action    String
  changes   Json?
  timestamp DateTime @default(now())
  invoice   Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([invoiceId])
  @@index([userId])
}

model CurrencyExchangeRate {
  id           String   @id @default(cuid())
  fromCurrency String
  toCurrency   String
  rate         Float
  date         DateTime
  source       String?
  createdAt    DateTime @default(now())

  @@index([date])
  @@index([fromCurrency, toCurrency])
}

model Chat {
  id         String    @id @default(uuid())
  createdAt  DateTime
  title      String
  userId     String
  visibility String    @default("private")
  user       User      @relation(fields: [userId], references: [id])
  messages   Message[]
  votes      Vote[]
}

model Message {
  id        String   @id @default(uuid())
  chatId    String
  role      String
  content   Json
  createdAt DateTime
  chat      Chat     @relation(fields: [chatId], references: [id], onDelete: Cascade)
  votes     Vote[]
}

model Vote {
  chatId    String
  messageId String
  isUpvoted Boolean
  chat      Chat    @relation(fields: [chatId], references: [id], onDelete: Cascade)
  message   Message @relation(fields: [messageId], references: [id], onDelete: Cascade)

  @@id([chatId, messageId])
}

model Document {
  id          String       @default(uuid())
  createdAt   DateTime
  title       String
  content     String?
  kind        String       @default("text")
  userId      String
  user        User         @relation(fields: [userId], references: [id])
  suggestions Suggestion[]

  @@id([id, createdAt])
}

model Suggestion {
  id                String   @id @default(uuid())
  documentId        String
  documentCreatedAt DateTime
  originalText      String
  suggestedText     String
  description       String?
  isResolved        Boolean  @default(false)
  userId            String
  createdAt         DateTime
  document          Document @relation(fields: [documentId, documentCreatedAt], references: [id, createdAt])
  user              User     @relation(fields: [userId], references: [id])
}

model ExportHistory {
  id             String        @id @default(cuid())
  exportId       String
  fileName       String
  fileUrl        String
  format         String
  count          Int
  folderName     String?
  createdAt      DateTime      @default(now())
  userId         String
  organizationId String?
  folderId       String?
  reportId       String?
  folder         ExportFolder? @relation(fields: [folderId], references: [id])
  organization   Organization? @relation(fields: [organizationId], references: [id])
  report         Report?       @relation(fields: [reportId], references: [id])
  user           User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([organizationId])
  @@index([folderId])
  @@index([reportId])
}

model VendorTypePreference {
  id          String   @id @default(cuid())
  userId      String
  vendorName  String
  invoiceType String
  confidence  Float    @default(0.7)
  lastUpdated DateTime @default(now())
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, vendorName])
}

model ExportFolder {
  id           String          @id @default(cuid())
  name         String
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt
  userId       String
  invoiceCount Int             @default(0)
  user         User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  exports      ExportHistory[]
  invoices     FolderInvoice[]

  @@index([userId])
}

model FolderInvoice {
  folderId  String
  invoiceId String
  folder    ExportFolder @relation(fields: [folderId], references: [id], onDelete: Cascade)
  invoice   Invoice      @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@id([folderId, invoiceId])
  @@index([folderId])
  @@index([invoiceId])
}

model Report {
  id               String            @id @default(cuid())
  title            String
  description      String?
  reportType       String
  format           String            @default("PDF")
  fileUrl          String?
  startDate        DateTime?
  endDate          DateTime?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  userId           String
  organizationId   String?
  isTemplate       Boolean           @default(false)
  templateId       String?
  exports          ExportHistory[]
  organization     Organization?     @relation(fields: [organizationId], references: [id])
  template         ReportTemplate?   @relation(fields: [templateId], references: [id])
  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  data             ReportData[]
  downloads        ReportDownload[]
  emails           ReportEmail[]
  scheduledReports ScheduledReport[]

  @@index([userId])
  @@index([organizationId])
  @@index([reportType])
}

model ReportData {
  id        String   @id @default(cuid())
  reportId  String
  invoiceId String?
  dataPoint String
  value     Float
  label     String?
  category  String?
  createdAt DateTime @default(now())
  invoice   Invoice? @relation(fields: [invoiceId], references: [id])
  report    Report   @relation(fields: [reportId], references: [id], onDelete: Cascade)

  @@index([reportId])
  @@index([invoiceId])
}

model ReportTemplate {
  id                String        @id @default(cuid())
  name              String
  description       String?
  reportType        String
  filters           Json?
  visualizationType String
  isAIGenerated     Boolean       @default(false)
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  userId            String
  organizationId    String?
  reports           Report[]
  organization      Organization? @relation(fields: [organizationId], references: [id])
  user              User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([organizationId])
  @@index([reportType])
}

model ScheduledReport {
  id             String        @id @default(cuid())
  reportId       String
  frequency      String
  nextRunTime    DateTime
  dayOfWeek      Int?
  dayOfMonth     Int?
  timeOfDay      String?
  emailAddresses String
  includeCharts  Boolean       @default(true)
  chartTypes     String?
  format         String        @default("PDF")
  relativePeriod String?
  active         Boolean       @default(true)
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  userId         String
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  report         Report        @relation(fields: [reportId], references: [id], onDelete: Cascade)
  user           User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([reportId])
  @@index([userId])
  @@index([organizationId])
  @@index([nextRunTime])
}

model ReportEmail {
  id             String   @id @default(cuid())
  reportId       String
  userId         String
  emailAddresses String
  subject        String?
  message        String?
  sentAt         DateTime @default(now())
  report         Report   @relation(fields: [reportId], references: [id], onDelete: Cascade)
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([reportId])
  @@index([userId])
}

model ReportDownload {
  id           String   @id @default(cuid())
  reportId     String
  userId       String
  downloadedAt DateTime @default(now())
  report       Report   @relation(fields: [reportId], references: [id], onDelete: Cascade)
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([reportId])
  @@index([userId])
}

model TeamInvite {
  id             String       @id @default(cuid())
  email          String
  role           UserRole     @default(VIEWER)
  token          String       @unique
  expiresAt      DateTime
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  invitedById    String
  organizationId String
  status         InviteStatus @default(PENDING)
  invitedBy      User         @relation("InvitedByUser", fields: [invitedById], references: [id])
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([email])
  @@index([token])
  @@index([organizationId])
  @@index([invitedById])
}

model WebhookEvent {
  createdAt       DateTime @default(now()) @map("createdAt")
  eventName       String   @map("eventName")
  processed       Boolean? @default(false)
  body            Json
  processingError String?  @map("processingError")
  id              Int      @id

  @@map("webhookEvent")
}

model Plan {
  id                 Int            @id @default(autoincrement())
  productId          Int            @map("productId")
  productName        String?        @map("productName")
  variantId          Int            @unique @map("variantId")
  name               String
  description        String?
  price              String
  isUsageBased       Boolean?       @default(false) @map("isUsageBased")
  interval           String?
  intervalCount      Int?           @map("intervalCount")
  trialInterval      String?        @map("trialInterval")
  trialIntervalCount Int?           @map("trialIntervalCount")
  sort               Int?
  paddlePriceId      String?        @unique @map("paddlePriceId")
  // Usage limits
  chatLimit          Int?           @map("chatLimit")
  invoiceLimit       Int?           @map("invoiceLimit")
  subscriptions      Subscription[]

  @@map("plan")
}

model Subscription {
  id                   Int      @id @default(autoincrement())
  orderId              Int?     @map("orderId")
  name                 String
  email                String
  status               String
  statusFormatted      String   @map("statusFormatted")
  renewsAt             String?  @map("renewsAt")
  endsAt               String?  @map("endsAt")
  trialEndsAt          String?  @map("trialEndsAt")
  price                String
  isUsageBased         Boolean? @default(false) @map("isUsageBased")
  isPaused             Boolean? @default(false) @map("isPaused")
  subscriptionItemId   Int      @default(autoincrement()) @map("subscriptionItemId")
  userId               String   @map("userId")
  planId               Int      @map("planId")
  cancelUrl            String?  @map("cancelUrl")
  createdAt            DateTime @default(now())
  paddleCustomerId     String?  @map("paddleCustomerId")
  paddlePriceId        String?  @map("paddlePriceId")
  paddleProductId      String?  @map("paddleProductId")
  paddleSubscriptionId String?  @unique @map("paddleSubscriptionId")
  paddleTransactionId  String?  @map("paddleTransactionId")
  provider             String   @default("paddle")
  updateUrl            String?  @map("updateUrl")
  updatedAt            DateTime @updatedAt
  plan                 Plan     @relation(fields: [planId], references: [id])
  user                 User     @relation(fields: [userId], references: [id])

  @@map("subscription")
}

model EmailIntegration {
  id           String   @id @default(cuid())
  userId       String
  provider     String   @default("gmail")
  email        String?
  accessToken  String
  refreshToken String?
  expiresAt    DateTime
  active       Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([provider])
}

model EmailSyncJob {
  id          String    @id @default(cuid())
  userId      String
  provider    String    @default("gmail")
  frequency   String    @default("daily")
  lastRun     DateTime?
  autoProcess Boolean   @default(true)
  includeRead Boolean   @default(true)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, provider])
  @@index([userId])
  @@index([provider])
}

model EmailSyncHistory {
  id             String   @id @default(cuid())
  userId         String
  provider       String   @default("gmail")
  processedCount Int      @default(0)
  status         String   @default("success")
  notes          String?
  createdAt      DateTime @default(now())
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([provider])
  @@index([createdAt])
}

model UserUsage {
  id            String   @id @default(cuid())
  userId        String
  chatUsage     Int      @default(0)
  invoiceUsage  Int      @default(0)
  resetDate     DateTime @default(now())
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId])
  @@index([userId])
  @@index([resetDate])
}

model InvoiceEnhancementJob {
  id               String    @id @default(cuid())
  invoiceId        String
  userId           String
  status           String    // 'pending', 'processing', 'completed', 'failed'
  enhancementTypes String[]  // ['fraud', 'related', 'payment', 'vendor', 'all']
  startedAt        DateTime  @default(now())
  completedAt      DateTime?
  error            String?
  results          Json?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  user             User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  invoice          Invoice   @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([invoiceId])
  @@index([status])
}

model UsersApiKey {
  id        String    @id @default(uuid()) @map("users_api_key_id")
  userId    String    @map("user_id")
  name      String?   @unique @map("name")
  apiKey    String    @unique @map("api_key")
  createdAt DateTime  @default(now()) @map("created_at")
  expiresAt DateTime? @map("expires_at")
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("users_api_key")
  @@index([apiKey])
  @@index([userId])
  @@index([name])
}

model HelpAndSupport {
  id          String   @id @default(cuid())
  name        String   @db.VarChar(255)
  phoneNumber String?  @map("phone_number") @db.VarChar(255)
  userId      String?  @map("user_id") @db.VarChar(255)
  email       String   @db.VarChar(255)
  message     String   @db.Text
  status      String   @default("open") @db.VarChar(50)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("help_and_support")
  @@index([status])
  @@index([userId])
  @@index([createdAt])
}

model Tool {
  id           String   @id @default(cuid()) @map("tool_id")
  name         String   @db.VarChar(255)
  description  String?  @db.Text
  toolConfig   Json?    @map("tool_config")
  sqlTemplate  String?  @map("sql_template") @db.Text
  createdAt    DateTime @default(now()) @map("created_at")

  @@map("tools")
  @@index([name])
}

model ApiUsage {
  id           String   @id @default(cuid())
  userId       String   @map("userId")
  chatUsage    Int      @default(0) @map("chatUsage")
  invoiceUsage Int      @default(0) @map("invoiceUsage")
  resetDate    DateTime @default(now()) @map("resetDate")
  createdAt    DateTime @default(now()) @map("createdAt")
  updatedAt    DateTime @updatedAt @map("updatedAt")
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_usage")
  @@unique([userId])
  @@index([userId])
  @@index([resetDate])
}

enum UserRole {
  ADMIN
  EDITOR
  USER
  VIEWER
}

enum InvoiceStatus {
  PENDING
  PAID
  OVERDUE
  CANCELLED
}

enum ReportType {
  INVOICE_SUMMARY
  CASH_FLOW
  PROFIT_LOSS
  VENDOR_ANALYSIS
  CATEGORY_BREAKDOWN
  BALANCE_SHEET
  SALES
  EXPENSES
  CATEGORY_ANALYSIS
  TAX
  CUSTOM
}

enum InviteStatus {
  PENDING
  ACCEPTED
  EXPIRED
  REVOKED
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum Visibility {
  PUBLIC
  PRIVATE
}
